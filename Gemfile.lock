GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.1.4)
      actionpack (= 7.1.4)
      activesupport (= 7.1.4)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.1.4)
      actionpack (= 7.1.4)
      activejob (= 7.1.4)
      activerecord (= 7.1.4)
      activestorage (= 7.1.4)
      activesupport (= 7.1.4)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.1.4)
      actionpack (= 7.1.4)
      actionview (= 7.1.4)
      activejob (= 7.1.4)
      activesupport (= 7.1.4)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (7.1.4)
      actionview (= 7.1.4)
      activesupport (= 7.1.4)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (7.1.4)
      actionpack (= 7.1.4)
      activerecord (= 7.1.4)
      activestorage (= 7.1.4)
      activesupport (= 7.1.4)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.1.4)
      activesupport (= 7.1.4)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activeadmin (3.2.4)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activejob (7.1.4)
      activesupport (= 7.1.4)
      globalid (>= 0.3.6)
    activemodel (7.1.4)
      activesupport (= 7.1.4)
    activerecord (7.1.4)
      activemodel (= 7.1.4)
      activesupport (= 7.1.4)
      timeout (>= 0.4.0)
    activestorage (7.1.4)
      actionpack (= 7.1.4)
      activejob (= 7.1.4)
      activerecord (= 7.1.4)
      activesupport (= 7.1.4)
      marcel (~> 1.0)
    activesupport (7.1.4)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    acts_as_list (1.2.2)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aes_key_wrap (1.1.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    anthropic (0.3.2)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    appdev_support (0.2.1)
      tabulo
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    ast (2.4.2)
    awesome_print (1.9.2)
    base64 (0.2.0)
    bcrypt (3.1.20)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.8)
    bindata (2.5.0)
    bindex (0.8.1)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    bootsnap (1.18.4)
      msgpack (~> 1.2)
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    choice (0.2.0)
    chronic (0.10.2)
    clamav-client (3.2.0)
    coderay (1.1.3)
    color (1.8)
    color_namer (0.1.0)
      color (>= 1.4.1)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    csso-rails (1.0.0)
      execjs (>= 1)
    csv (3.3.0)
    database_cleaner (2.1.0)
      database_cleaner-active_record (>= 2, < 3)
    database_cleaner-active_record (2.2.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.4)
    debug (1.9.2)
      irb (~> 1.10)
      reline (>= 0.3.8)
    debug_inspector (1.2.0)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-security (0.18.0)
      devise (>= 4.3.0)
    devise-two-factor (5.1.0)
      activesupport (~> 7.0)
      devise (~> 4.0)
      railties (~> 7.0)
      rotp (~> 6.0)
    diff-lcs (1.5.1)
    domain_name (0.6.20240107)
    dotenv (3.1.2)
    dotenv-rails (3.1.2)
      dotenv (= 3.1.2)
      railties (>= 6.1)
    draft_generators (0.0.4)
      devise
      indefinite_article
    draft_matchers (0.0.2)
      capybara
      color_namer
      rspec
    drb (2.2.1)
    email_validator (2.2.4)
      activemodel
    erubi (1.13.0)
    et-orbi (1.2.11)
      tzinfo
    event_stream_parser (1.0.0)
    execjs (2.9.1)
    factory_bot (6.5.1)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.4.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.11.0)
      faraday-net_http (>= 2.0, < 3.4)
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-multipart (1.1.0)
      multipart-post (~> 2.0)
    faraday-net_http (3.3.0)
      net-http
    faraday-retry (1.0.3)
    ffi (1.17.0-aarch64-linux-gnu)
    ffi (1.17.0-arm64-darwin)
    ffi (1.17.0-x86_64-linux-gnu)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    get_process_mem (1.0.0)
      bigdecimal (>= 2.0)
      ffi (~> 1.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    good_job (4.2.1)
      activejob (>= 6.1.0)
      activerecord (>= 6.1.0)
      concurrent-ruby (>= 1.3.1)
      fugit (>= 1.11.0)
      railties (>= 6.1.0)
      thor (>= 1.0.0)
    grade_runner (0.0.12)
      activesupport (>= 2.3.5)
      faraday-retry (~> 1.0.3)
      octokit (~> 5.0)
      oj (~> 3.13.12)
      rake (~> 13)
      zip
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.1.1)
    hashie (5.0.0)
    htmlbeautifier (1.4.3)
    http (5.2.0)
      addressable (~> 2.8)
      base64 (~> 0.1)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    importmap-rails (2.0.1)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    indefinite_article (0.2.5)
      activesupport
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.7.2)
    irb (1.14.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.12.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.7.2)
    json-jwt (1.16.6)
      activesupport (>= 4.2)
      aes_key_wrap
      base64
      bindata
      faraday (~> 2.0)
      faraday-follow_redirects
    jwt (2.8.2)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    llhttp-ffi (0.5.0)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.6.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mini_mime (1.1.5)
    minitest (5.25.1)
    msgpack (1.7.2)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mutex_m (0.2.0)
    net-http (0.4.1)
      uri
    net-imap (0.4.15)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    nio4r (2.7.3)
    nokogiri (1.18.7-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.7-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.7-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth (1.1.0)
      oauth-tty (~> 1.0, >= 1.0.1)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    oauth-tty (1.0.5)
      version_gem (~> 1.1, >= 1.1.1)
    oauth2 (2.0.9)
      faraday (>= 0.17.3, < 3.0)
      jwt (>= 1.0, < 3.0)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0)
      version_gem (~> 1.1)
    octokit (5.6.1)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    oj (3.13.23)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-apple (1.3.0)
      json-jwt
      omniauth-oauth2
    omniauth-facebook (10.0.0)
      bigdecimal
      omniauth-oauth2 (>= 1.2, < 3)
    omniauth-google-oauth2 (1.1.2)
      jwt (>= 2.0)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth (1.2.1)
      oauth
      omniauth (>= 1.0, < 3)
      rack (>= 1.6.2, < 4)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    omniauth-twitter (1.4.0)
      omniauth-oauth (~> 1.1)
      rack
    openssl (3.2.0)
    orm_adapter (0.5.0)
    parallel (1.26.3)
    paranoia (2.6.4)
      activerecord (>= 5.1, < 7.2)
    parser (3.3.4.2)
      ast (~> 2.4.1)
      racc
    pdf-core (0.10.0)
    pg (1.5.7)
    prawn (2.5.0)
      matrix (~> 0.4)
      pdf-core (~> 0.10.0)
      ttfunk (~> 1.8)
    prawn-table (0.2.2)
      prawn (>= 1.3.0, < 3.0.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (5.1.2)
      stringio
    public_suffix (6.0.1)
    puma (6.4.2)
      nio4r (~> 2.0)
    pundit (2.4.0)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.12)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-protection (4.0.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (7.1.4)
      actioncable (= 7.1.4)
      actionmailbox (= 7.1.4)
      actionmailer (= 7.1.4)
      actionpack (= 7.1.4)
      actiontext (= 7.1.4)
      actionview (= 7.1.4)
      activejob (= 7.1.4)
      activemodel (= 7.1.4)
      activerecord (= 7.1.4)
      activestorage (= 7.1.4)
      activesupport (= 7.1.4)
      bundler (>= 1.15.0)
      railties (= 7.1.4)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-erd (1.7.2)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
      choice (~> 0.2.0)
      ruby-graphviz (~> 1.2)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rails_db (2.4.4)
      activerecord
      csv
      kaminari
      rails (>= 5.0.0)
      ransack (>= 2.3.2)
      simple_form (>= 5.0.1)
      sprockets-rails
      terminal-table
    railties (7.1.4)
      actionpack (= 7.1.4)
      activesupport (= 7.1.4)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    ransack (4.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rdoc (6.7.0)
      psych (>= 4.0.0)
    recaptcha (5.17.0)
    redis (4.8.1)
    redis-actionpack (5.5.0)
      actionpack (>= 5)
      redis-rack (>= 2.1.0, < 4)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.3.0)
      activesupport (>= 3, < 8)
      redis-store (>= 1.3, < 2)
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-rack (3.0.0)
      rack-session (>= 0.2.0)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-store (1.11.0)
      redis (>= 4, < 6)
    regexp_parser (2.9.2)
    reline (0.5.10)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.3.6)
      strscan
    rotp (6.3.0)
    rouge (4.3.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-html-matchers (0.10.0)
      nokogiri (~> 1)
      rspec (>= 3.0.0.a)
    rspec-mocks (3.13.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.0.4)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.12)
      rspec-expectations (~> 3.12)
      rspec-mocks (~> 3.12)
      rspec-support (~> 3.12)
    rspec-support (3.13.1)
    rubocop (1.65.1)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.4, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.32.1)
      parser (>= *******)
    ruby-graphviz (1.2.5)
      rexml
    ruby-openai (7.4.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    rufo (0.18.0)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    simple_form (5.3.1)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    snaky_hash (2.0.1)
      hashie
      version_gem (~> 1.1, >= 1.1.1)
    specs_to_readme (0.1.0)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (1.7.3-aarch64-linux)
    sqlite3 (1.7.3-arm64-darwin)
    sqlite3 (1.7.3-x86_64-linux)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.1)
    strscan (3.1.0)
    table_print (1.5.7)
    tabulo (3.0.2)
      tty-screen (= 0.8.2)
      unicode-display_width (~> 2.5)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    thor (1.3.2)
    tilt (2.4.0)
    timeout (0.4.1)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    tty-screen (0.8.2)
    turbo-rails (2.0.6)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    uri (1.0.3)
    version_gem (1.1.4)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webmock (3.23.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.0)
    websocket (1.2.11)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.18)
    zip (2.0.2)

PLATFORMS
  aarch64-linux
  arm64-darwin-23
  x86_64-linux

DEPENDENCIES
  activeadmin
  acts_as_list
  annotate
  anthropic (~> 0.3.2)
  appdev_support
  awesome_print
  better_errors
  binding_of_caller
  bootsnap
  capybara
  clamav-client
  csso-rails
  database_cleaner
  debug
  devise
  devise-security
  devise-two-factor
  dotenv-rails
  draft_generators
  draft_matchers
  email_validator
  factory_bot_rails
  faker
  get_process_mem
  good_job
  grade_runner
  htmlbeautifier
  http
  httparty
  importmap-rails
  jbuilder
  jquery-rails
  kaminari
  omniauth
  omniauth-apple
  omniauth-facebook
  omniauth-google-oauth2
  omniauth-rails_csrf_protection
  omniauth-twitter
  openssl (~> 3.2.0)
  parallel
  paranoia (~> 2.6)
  pg (~> 1.1)
  prawn
  prawn-table
  pry-rails
  puma
  pundit
  rack-attack
  rails (~> 7.1.3, >= *******)
  rails-erd
  rails_db
  recaptcha
  redis (~> 4.8)
  redis-namespace (~> 1.10)
  redis-rails (~> 5.0)
  rexml (~> 3.3.6)
  rspec-html-matchers
  rspec-rails (~> 6.0.0)
  rubocop
  ruby-openai
  rufo
  sassc-rails
  selenium-webdriver
  shoulda-matchers
  sitemap_generator
  specs_to_readme
  sprockets-rails
  sqlite3 (~> 1.4)
  stimulus-rails
  table_print
  turbo-rails
  turbolinks (~> 5)
  tzinfo-data
  web-console
  webdrivers
  webmock
  whenever

RUBY VERSION
   ruby 3.2.1p31

BUNDLED WITH
   2.4.6
