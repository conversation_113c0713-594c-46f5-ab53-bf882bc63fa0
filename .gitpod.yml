image: firstdraft/appdev-rails-7-1-template

tasks:
  - before: |
      sudo cp -r /home/<USER>/home/<USER>
      (cd /home/<USER>/student; sudo find . -maxdepth 1 -exec mv {} .. \;)
      source ~/.bashrc
      sudo chmod 777 /home/<USER>/home/<USER>/home/<USER>
      export GEM_HOME=/workspace/.rvm
      export GEM_PATH=$GEM_HOME:$GEM_PATH
      export PATH=/workspace/.rvm/bin:$PATH
      bin/setup
ports:
  - port: 3000
    onOpen: open-preview
    visibility: public
  - port: 4567
    onOpen: open-preview
    visibility: public
  - port: 9292
    onOpen: open-preview
    visibility: public
  - port: 5432
    onOpen: ignore

vscode:
  extensions:
    - vortizhe.simple-ruby-erb
    - mbessey.vscode-rufo
    - aliariff.vscode-erb-beautify
    - eamodio.gitlens
    - setobiralo.erb-commenter
