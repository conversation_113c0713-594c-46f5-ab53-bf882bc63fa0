﻿Revised Research Plan for Migrating a Ruby on Rails Application to Google Cloud Platform's Free Tier




1. Executive Summary


This report outlines a revised research plan for migrating an existing Ruby on Rails web application, currently hosted on Render, to the Google Cloud Platform (GCP) free tier. The principal objective is to achieve zero ongoing maintenance costs, a critical requirement for an application in its development phase with no active user base. The proposed migration strategy leverages a suite of GCP and Firebase services, including Cloud Run for compute, Firestore for database services with native vector search, Firebase Authentication for user management, and Gemini models for AI-driven features.
Analysis indicates that, with meticulous planning and adherence to free tier limitations, operating the application at no cost is feasible. However, achieving this requires more than selecting services with individual free allowances; it demands careful orchestration to ensure their combined usage, particularly for data operations and network egress, remains within both individual and potentially aggregated free tier thresholds. Critical considerations for maintaining this cost-free status include rigorous monitoring of Firestore operational quotas (document reads, writes, deletes, and vector index reads) and strict adherence to Gemini API rate limits. The research plan detailed herein focuses on validating these assumptions and providing a clear path for a cost-effective migration.


2. Current Application & Migration Goals Recap




Current Render Setup and Costs


The application currently operates on Render with a configuration consisting of one web instance and one background job instance. Data is managed by a 5GB PostgreSQL database. The application utilizes a custom domain, and the total monthly expenditure for this setup is $14.50. This existing infrastructure and cost provide the baseline against which the success of the GCP migration will be measured, with the explicit aim of reducing this recurring cost to zero.


Primary Goal: Zero-Cost Maintenance on GCP Free Tier


The foremost objective of this migration is to transition the Ruby on Rails application to GCP services that are part of the "always free" tier. This ensures no recurring monthly charges are incurred while the application remains in development and lacks an active user base. This necessitates a careful selection of services and usage patterns that avoid exceeding free quotas or requiring features that mandate a billing-enabled account. It is important to distinguish the "always free" tier from the initial $300 credit offered to new GCP customers.1 The "always free" tier allows all Google Cloud customers to use select products free of charge within specified monthly usage limits, and these resources are not charged against free trial credits or to a billing account's payment method after any trial period ends.2 This plan is exclusively focused on leveraging these perpetual free offerings.
Any component or operational pattern that necessitates enabling billing on the GCP project, even if the specific feature has a nominal free allowance post-billing enablement, presents a challenge to the "zero-cost" mandate. Several GCP services offer enhanced features or increased limits once billing is activated.3 Adherence to the strict "free to maintain" goal suggests these paths should be avoided unless a feature is indispensable and its usage can be meticulously managed to prevent any cost incurrence. If a critical feature, such as Firestore's Time-To-Live (TTL) deletes 3, is deemed essential and requires billing enablement, the zero-cost objective is immediately jeopardized, as it creates a pathway for potential overages on other services to accrue charges.


3. GCP Free Tier Hosting for Ruby on Rails Application




3.1. Compute Platform Analysis (Web & Background Worker)


The selection of an appropriate compute platform is foundational to achieving the zero-cost objective. Both Cloud Run and App Engine Standard Environment offer free tiers suitable for hosting Ruby on Rails applications, each with distinct characteristics.
Cloud Run
Cloud Run is a fully managed serverless platform that enables the deployment of containerized applications. Its free tier is particularly well-suited for applications with intermittent or no traffic.
* Web Server (Request-based Billing): For the web server component, Cloud Run's free tier includes 2 million requests per month, 180,000 vCPU-seconds per month, and 360,000 GiB-seconds of memory per month.1 A key advantage for a development-stage application with no users is Cloud Run's scale-to-zero capability. This means that if there are no incoming requests, the service can scale down to zero instances, incurring no charges for vCPU or memory during idle periods.5 Cloud Run charges only for the resources consumed during request processing, rounded to the nearest 100 milliseconds.5
* Background Worker (Instance-based Billing): For the background job instance, Cloud Run Jobs offer a separate free tier under instance-based billing: 240,000 vCPU-seconds per month and 450,000 GiB-seconds of memory per month.5 Requests are not charged under this model. Cloud Run Jobs are billed for the entire lifetime of any instance started, with a minimum billing duration of 1 minute per instance execution.5 This model is appropriate for tasks that run to completion, such as background processing.
The "pay for what you use" model of Cloud Run aligns well with the application's current no-user status. Tutorials and documentation confirm that Ruby on Rails applications can be effectively deployed on Cloud Run, often using Docker containers.6
App Engine (Standard Environment)
App Engine Standard Environment also provides a free tier.
* Instance Hours: It offers 28 free F1 instance-hours per day for frontend instances (suitable for the web server) and 9 free B1 instance-hours per day for backend instances (potentially for background workers).9
* Storage and Database: The free tier includes 1 GB for code and static data storage, and a 5 GB default Cloud Storage bucket.9 It also provides free daily quotas for Firestore in Datastore mode (or native Firestore, as it's part of the same underlying system): 1 GiB of stored data, 50,000 entity reads, 20,000 entity writes, and 20,000 entity deletes per day.9
* Task Queues: For background job processing, App Engine offers Task Queues with free tier limits on task size (100KB), execution rate (500 task invocations per second per queue), and the number of queues (100).9
Recommendation and Rationale
Cloud Run is the recommended compute platform for this migration. Its container-native approach and more granular resource allocation (vCPU-seconds and GiB-seconds) are generally more flexible and cost-effective for an application with no users than App Engine's daily instance-hour quotas. Cloud Run's ability to scale services to zero when there are no requests is a significant advantage for minimizing costs during the development phase.5 While App Engine's free instance hours are substantial, Cloud Run's model ensures that compute resources for the web server are only billed when actively processing requests. For background tasks, Cloud Run Jobs provide a clear and container-friendly mechanism that integrates well if the web tier is also on Cloud Run, maintaining consistency in deployment and management.11
Table 3.1.1: GCP Compute Free Tier Comparison (Cloud Run vs. App Engine Standard)
Feature
	Cloud Run Free Tier Limit (Tier 1 Regions)
	App Engine Standard Free Tier Limit
	Suitability for RoR Web (Zero Users)
	Suitability for RoR Background Job (Zero Users)
	Web Server Compute
	180,000 vCPU-seconds/month, 360,000 GiB-seconds/month, 2 million requests/month 1
	28 F1 instance-hours/day 9
	Excellent (scales to zero)
	N/A
	Background Job Compute
	240,000 vCPU-seconds/month, 450,000 GiB-seconds/month (for Jobs) 5
	9 B1 instance-hours/day 9
	N/A
	Good (dedicated job execution)
	Data Egress (Service)
	1 GiB/month (North America) 2
	1 GB/day (from default bucket) 9
	Good
	Good
	Custom Domain Support
	Firebase Hosting (recommended, free tier applies), Cloud Run Domain Mapping (Preview, limited availability) 12
	Native support
	Good (via Firebase Hosting)
	Good (via Firebase Hosting if exposed)
	Background Job Handling
	Cloud Run Jobs 5
	App Engine Task Queues, Backend Instances 9
	N/A
	Excellent
	Scalability
	Scales to zero for services; on-demand for jobs 5
	Scales to zero (configurable); automatic/basic/manual scaling options 9
	Excellent
	Good
	Container Support
	Yes (primary model)
	Yes (Flexible Environment), No (Standard Environment uses runtimes)
	Excellent
	Excellent
	Primary Cost Model
	Pay-per-use (CPU, memory, requests for services; instance time for jobs) 5
	Instance-hours, other resource quotas 9
	Excellent
	Good
	

3.2. Custom Domain Configuration


Maintaining the application's custom domain is a stated requirement. For a Cloud Run service, several options exist, with Firebase Hosting emerging as the most robust and cost-effective solution within the free tier.
* Firebase Hosting as a Proxy: This is the recommended method for mapping a custom domain to a Cloud Run service.12 Firebase Hosting provides a global CDN, automatic SSL certificate provisioning and renewal, and its own free tier: 10 GB of storage and 360 MB/day of data transfer.13 This should be ample for proxying requests to a Cloud Run service that has no active users, effectively offloading SSL management and potentially improving performance.
* Cloud Run Domain Mapping: Google Cloud offers a native domain mapping feature for Cloud Run services. However, this feature is currently in Preview, has limited regional availability, and is not recommended for production services due to potential latency issues.12 While it might be considered for a development application with no users if the service region is supported, Firebase Hosting offers a more mature and feature-rich solution.
* Domain Registration Costs: It is important to note that the registration of the custom domain itself is not covered by the GCP free tier and remains an external cost.10 The current plan assumes an existing custom domain.
The choice of Cloud Run as the compute platform steers the custom domain strategy towards Firebase Hosting, which integrates smoothly and leverages Firebase's infrastructure within its free allowance.


4. Database Migration: PostgreSQL to Firestore with Vector Search


Migrating from PostgreSQL to Firestore represents a significant architectural shift from a relational to a NoSQL document database model. This transition is coupled with the requirement to implement native vector search capabilities for user and content data.


4.1. Firestore Free Tier Deep Dive


Understanding Firestore's free tier limits is paramount to achieving the zero-cost objective.
* Storage and Operations: The free tier for Firestore (which applies to one default database per project) includes 1 GiB of total storage.4 This storage encompasses not only the raw data but also metadata and indexes.4 Daily operational quotas are: 50,000 document reads, 20,000 document writes, and 20,000 document deletes.1 Additionally, there is a 10 GiB per month allowance for outbound data transfer from Firestore.2 These quotas reset daily around midnight Pacific Time.4
* Native Vector Search: Firestore natively supports K-nearest neighbor (kNN) vector search, allowing for similarity searches on vector embeddings stored within documents.16 The cost of vector search queries is measured in read operations: one read operation is charged for each batch of up to 100 kNN vector index entries scanned by the query.4 A critical point of investigation is whether these kNN vector index read operations are deducted from the 50,000 free daily
document read operations. Current documentation does not explicitly confirm this inclusion.15 Given that vector index reads are not listed among features explicitly requiring billing, the working assumption for this plan must be that they
do count against the free document read quota, necessitating extremely careful query design and monitoring.
Vector search in Firestore has limitations, including a maximum embedding dimension of 2048, a maximum of 1000 documents returned from a kNN query, and no support for real-time snapshot listeners. Client library support for vector search is also specific to certain languages (Python, Node.js, Go, and Java are mentioned, implying Ruby support might require direct REST API calls or further investigation).19
* Features Requiring Billing: Several Firestore features are explicitly outside the always-free scope and require billing to be enabled on the project. These include Time-To-Live (TTL) deletes, Point-in-Time Recovery (PITR) data, backup data and restore operations, and the use of named (non-default) databases.3 Utilizing any of these features would compromise the strict zero-cost goal by necessitating billing enablement, which could lead to charges if other free tier limits are inadvertently exceeded.
The most significant ambiguity and potential cost risk in this migration revolves around how Firestore kNN vector index reads are counted. If each batch of 100 scanned kNN index entries consumes one "document read" from the daily free quota of 50,000, then unoptimized vector searches could rapidly exhaust this allowance. For instance, a single vector search query that scans 500,000 index entries (which is plausible for a moderately sized dataset without precise pre-filtering) would equate to 5,000 read operations, consuming 10% of the daily free document read quota instantly. This underscores the necessity for precise data modeling, efficient indexing strategies, and potentially direct clarification from Google Cloud support on this billing nuance.15
Table 4.1.1: Firestore Free Tier Limits (Per Project, Default Database)
Resource
	Daily/Monthly Limit
	Key Considerations for Zero Cost
	Stored Data
	1 GiB (total) 4
	Includes data, metadata, and indexes. Keep document sizes minimal.
	Document Reads
	50,000 per day 4
	Optimize queries, use caching. Critically, confirm if kNN index reads count towards this.
	Document Writes
	20,000 per day 4
	Batch writes for data migration. Minimize unnecessary writes.
	Document Deletes
	20,000 per day 4
	Consider soft deletes if TTL is needed but billing is to be avoided.
	kNN Vector Index Reads
	1 read op per 100 entries scanned 15
	Uncertain if part of 50k document read quota. Assume it is for planning. Optimize vector queries heavily (e.g., pre-filtering if possible, though this also costs reads).
	Outbound Data Transfer
	10 GiB per month 4
	Primarily for data leaving GCP. Intra-region traffic to Cloud Run should be free.
	Features Requiring Billing
	TTL deletes, PITR, Backups, Named Databases 4
	Avoid these features to maintain zero cost without enabling billing.
	

4.2. Data Modeling Strategy


Migrating from a relational PostgreSQL database to Firestore's NoSQL document model, especially when incorporating vector search, requires careful planning of data structures.
   * Migrating Relational Data (Users, Content) to NoSQL (Firestore): The core entities, users and content, will need to be modeled as Firestore collections and documents. Denormalization is often a key strategy in NoSQL to optimize for read performance and reduce complex joins. Common Firestore data structuring patterns include using root-level collections for primary entities (e.g., users, content_items), subcollections for tightly coupled related data that is always queried in the context of a parent document, or embedding data directly within documents for small, fixed sets of related information.21 For this application,
users and content_items (or similar) as root collections are likely appropriate, allowing flexible querying. User-generated content would typically store a reference (e.g., userId) to the user who created it. Avoid using monotonically increasing document IDs to prevent hotspots.23
   * Structuring Data for Vector Search: Vector embeddings, generated from user or content data, will be stored as a specific vector field type within the corresponding Firestore documents.17 For example, a
content_items document might have a field like embedding: FieldValue.vector([0.1, 0.2,...]). A vector index must then be created on this embedding field to enable kNN searches.19 If searching user data, user profiles would similarly contain an embedding field. Linking user data to their content for search purposes could involve querying content based on
userId and then performing vector searches within that subset, or performing broader vector searches and filtering by userId if applicable.
The transition from SQL's structured schemas and JOIN capabilities to Firestore's flexible but query-constrained model means that query patterns heavily influence data structure. Operations that were simple in PostgreSQL might require careful schema design or multiple queries in Firestore, each contributing to the daily read operation count.


4.3. Data Migration Process Outline


The data migration will involve exporting data from PostgreSQL, transforming it (including generating vector embeddings), and importing it into Firestore.
      * Exporting from PostgreSQL: Data can be exported from PostgreSQL using standard tools like pg_dump to formats such as CSV or JSON.25 Alternatively, a Ruby script utilizing the
pg gem can connect to the PostgreSQL database, retrieve data row by row, and convert it into a structured format like JSON objects, ready for transformation.26
      * Transforming Data and Generating Embeddings: A Ruby script will be essential for this stage.
         1. Read Exported Data: The script will parse the data exported from PostgreSQL (e.g., from JSON files).
         2. Generate Vector Embeddings: For each user profile or piece of content requiring vector search capabilities, the script will call a Gemini embedding model (e.g., gemini-embedding-experimental-03-07 or a successor like text-embedding-004 via Vertex AI SDKs if more convenient for Ruby).30 This process must be carefully managed:
         * Batching: API calls to Gemini should be batched where possible. The Vertex AI API, for instance, allows up to 250 input texts per request for some embedding models.31 Gemini API rate limits (RPM, RPD, TPM, TPD) must be strictly adhered to.30
         * Rate Limiting & Retries: The script must implement delays and potentially exponential backoff retry mechanisms to avoid exceeding these rate limits, especially for bulk processing of initial data.32
         3. Data Transformation: The script will transform the original relational data into the designed Firestore document structure, incorporating the newly generated vector embedding into the appropriate field.
         * Importing into Firestore: The google-cloud-firestore Ruby gem will be used to write the transformed data, including embeddings, into Firestore.33 To manage Firestore's daily write limit of 20,000 documents and to improve efficiency, data should be imported using batch writes. The Firestore SDKs allow batching up to 500 operations (creates, updates, deletes) in a single atomic commit [51 (conceptual example for Ruby)]. The migration script will need to break down the import into appropriately sized batches.
Given the free tier constraints on both Gemini API calls for embedding generation and Firestore daily write operations, the initial data migration, if substantial, might need to be a carefully orchestrated, multi-day process. For an application currently stated as having "no users" and 5GB of PostgreSQL data, the volume might be manageable within a reasonable timeframe, but this depends on the actual number of records needing embeddings.


5. Authentication: Devise to Firebase Authentication


Replacing the existing Devise authentication system with Firebase Authentication is a key part of the migration, aiming to leverage Firebase's robust and free authentication services.


5.1. Firebase Authentication (Spark Plan)


Firebase Authentication's free "Spark Plan" offers generous limits suitable for an application in development or with a small user base.
         * User Limits: The Spark Plan supports up to 50,000 Monthly Active Users (MAU) and 3,000 Daily Active Users (DAU) for standard authentication methods like email/password and social logins (e.g., Google, Facebook).34 It also supports up to 100 million anonymous user accounts.35 These limits are more than sufficient for an application with no current users.
         * Email Sending Limits: For operational emails, the free tier has limits: 150 password reset emails per day and 5 email link sign-in emails per day.35 While low, the email link sign-in limit is unlikely to be an issue for a development application; password resets should also be well within limits.
         * Features Requiring Blaze Plan: It's important to note that more advanced features, such as custom OIDC or SAML providers, or phone authentication via SMS, require upgrading to the paid "Blaze Plan".34 For the current scope, these are not required.
Table 5.1.1: Firebase Authentication (Spark Plan) Free Tier Limits
Feature
	Limit
	Notes
	Monthly Active Users (MAU) - Tier 1
	50,000/month 34
	Email/password, prebuilt social, anonymous.
	Daily Active Users (DAU) - Tier 1
	3,000/day 34
	

	Anonymous User Accounts
	100 million 35
	

	Password Reset Emails
	150/day 35
	Sufficient for development.
	Email Link Sign-in Emails
	5/day 35
	Very limited; consider if this is a primary sign-in method. Billing instrument needed to exceed.35
	Custom OIDC/SAML Providers
	Not available on Spark Plan (Requires Blaze Plan) 34
	N/A for current scope.
	Phone Authentication (SMS)
	Not available on Spark Plan (Requires Blaze Plan for SMS sending) 34
	N/A for current scope.
	New Account Creation (per IP)
	100 accounts/hour 35
	Protection against abuse.
	

5.2. Integration with Ruby on Rails Backend


Integrating Firebase Authentication with a Ruby on Rails backend involves the backend verifying ID tokens issued by Firebase to clients.
         * Verifying Firebase ID Tokens in Rails:
The standard flow involves the client application (web or future mobile) handling the sign-in process using Firebase SDKs. Upon successful authentication, the client receives a Firebase ID Token (a JWT). This token is then sent to the Ruby on Rails backend with API requests. The Rails backend must then verify this ID token to authenticate the user.
Since the Firebase Admin SDK does not offer native Ruby support for the verifyIdToken method 36, a third-party Ruby JWT library, such as
ruby-jwt, must be employed. The verification process involves several steps:
            1. Fetch Google's Public Keys: The backend needs to retrieve Google's public RSA signing keys, which are used to sign Firebase ID tokens. These keys are available at a specific Google endpoint (https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>).36
            2. Cache Public Keys: To avoid fetching these keys on every request (which would be inefficient and could lead to rate-limiting), they must be cached. The Cache-Control header in the HTTP response from the key endpoint specifies the max-age for these keys, dictating how long they can be cached.36 The
firebase_id_token Ruby gem offers a solution using Redis for caching.38 For a simpler setup aiming for zero external dependencies beyond GCP free services, an in-memory cache with a TTL (Time-To-Live) or a file-based cache within Cloud Run's temporary storage could be considered, though Redis (e.g., via Memorystore, which has no free tier) would be more robust for multi-instance scenarios if the application scales.
            3. Decode and Verify JWT: Using the cached public key corresponding to the kid (Key ID) in the ID token's header, the ruby-jwt library can verify the token's signature. Additionally, several claims within the token's payload must be validated:
               * exp (Expiration time): Must be in the future.
               * iat (Issued at time): Must be in the past.
               * aud (Audience): Must be the Firebase Project ID.
               * iss (Issuer): Must be https://securetoken.google.com/<YOUR_FIREBASE_PROJECT_ID>.
               * sub (Subject): Must be a non-empty string, representing the Firebase User ID (uid).
               * auth_time (Authentication time): Must be in the past.

36

The lack of official Firebase Admin SDK support for Ruby token verification means this process requires careful, custom implementation. While gems like firebase_id_token 38 can abstract some of this complexity, they might introduce dependencies like Redis, which would then also need to be managed within free tier constraints (Memorystore for Redis does not have a significant free tier).
                  * Session Management Strategies:
Once a Firebase ID token has been successfully verified by the Rails backend, the application can establish its own session for the user. The Firebase uid extracted from the token should be stored in the Rails session (e.g., using the standard Rails cookie-based session store). This uid then serves as the user's identifier for subsequent requests to the backend, avoiding the need to re-verify the Firebase ID token on every single request. Firebase ID tokens themselves are short-lived, typically expiring after one hour 39, making them unsuitable for long-term session management directly. While Firebase also offers server-side session cookie management capabilities 40, integrating with the standard Rails session mechanism after initial token validation is generally simpler for a traditional web application.
                  * Linking Firebase Auth UIDs with Firestore User Data:
The Firebase uid obtained from the verified ID token is the crucial link between the authentication system and the application's user data stored in Firestore. This uid should be used as the document ID for user-specific documents in a users collection (or a similarly named collection) in Firestore. This allows the application to associate profiles, preferences, content created by the user, and other application-specific data directly with the authenticated Firebase user.16
The decision to use an external authentication provider like Firebase necessitates a robust mechanism within the Rails application for token verification and linking authenticated identities to application data. While more involved than Devise's integrated approach, it provides a scalable and platform-agnostic authentication solution.


6. AI Capabilities with Gemini Models


Integrating AI capabilities using Google's Gemini models is a key requirement, with the constraint of using only free tier offerings.


6.1. Gemini API Free Tier


The Gemini API provides access to powerful multimodal models, and a free tier is available, primarily characterized by rate limits rather than direct charges for model usage itself.
                     * Applicable Models:
                     * For Embedding Generation: The "Gemini Embedding Experimental 03-07" model (or its successors like text-embedding-004 often referenced in Vertex AI contexts, which Gemini API may provide access to) is designed for creating vector embeddings from text.30 These embeddings are essential for the Firestore vector search functionality.
                     * For General Text Tasks: For features like content generation, summarization, or Q&A, models such as "Gemini 1.5 Flash" are suitable due to their balance of capability and efficiency.30

The free tier for these models typically means that input and output are free of charge, but usage is constrained by rate limits.43
                        * Rate Limits and Token Counting:
                        * Rate Limits: The free tier imposes strict rate limits, measured in Requests Per Minute (RPM), Requests Per Day (RPD), Tokens Per Minute (TPM), and Tokens Per Day (TPD).30 These limits vary by model. For example:
                        * Gemini Embedding Experimental 03-07: 5 RPM, 100 RPD (TPM not specified as primary metric).30
                        * Gemini 1.5 Flash: 15 RPM, 250,000 TPM, 500 RPD.30
                        * Token Counting: Understanding how tokens are counted is crucial for managing TPM/TPD limits. For Gemini models, a token is approximately equivalent to 4 characters of text, and 100 tokens represent about 60-80 English words.44 Google provides a
CountTokens API that can be used to estimate the number of input tokens for a given request, helping to stay within model context windows and rate limits.44
The primary constraint when using Gemini models under the free tier is not the cost per call (which is $0 for input/output within limits), but the frequency and volume of calls allowed. For an application in development with no users, these rate limits should be manageable for on-demand AI features. However, bulk operations, such as generating embeddings for an initial dataset, will require careful batching and adherence to these limits.
Table 6.1.1: Gemini API Free Tier Rate Limits (Selected Models - Subject to Change, Verify with Current Documentation)
Model Purpose
	Specific Model Name (Example)
	RPM (Free Tier)
	RPD (Free Tier)
	TPM (Free Tier)
	TPD (Free Tier)
	Key Use Case for this Application
	Text Embedding
	Gemini Embedding Experimental 03-07
	5 30
	100 30
	--
	--
	Generating vector embeddings for Firestore
	General Text Generation
	Gemini 1.5 Flash
	15 30
	500 30
	250,000 30
	Not explicitly listed, covered by TPM/RPD
	Content generation, summarization, Q&A
	General Text Generation
	Gemini 2.0 Flash
	15 30
	1,500 30
	1,000,000 30
	Not explicitly listed
	Alternative for text generation
	Note: Rate limits are per project and can change. Always refer to the latest official Google AI documentation for current limits.


6.2. Integration Strategy


Integrating Gemini models into the Ruby on Rails application will primarily occur at the backend.
                           * Generating Vector Embeddings for Firestore Data:
Vector embeddings for user profiles and content will be generated by calling a suitable Gemini embedding model.
                              * Bulk Processing (Initial Data): If there's an existing dataset to migrate from PostgreSQL, or for any bulk import operations, a Ruby script will be needed. This script should iterate through the data, make batched calls to the Gemini embedding API, and implement delays and exponential backoff retry logic to respect the API's RPM and RPD limits.30 For instance, the Vertex AI API (which may be used to access some embedding models) has limits like 250 input texts per request for certain models.31 The generated embeddings will then be stored in the designated vector fields in Firestore documents.17
                              * On-the-fly Generation (New Data): When new content is created or user profiles are updated, embeddings can be generated in real-time. This operation must be efficient to avoid degrading the user experience and must still operate within the per-minute rate limits of the Gemini API. This might involve triggering a background job (e.g., a Cloud Run Job) to handle the embedding generation asynchronously if the process is too slow for a synchronous request-response cycle.
                              * Implementing other AI features (e.g., content generation):
For other AI-driven functionalities, such as generating text summaries, answering questions based on application content, or creating new content, a general-purpose Gemini text model like Gemini 1.5 Flash can be invoked from the Rails backend.42 As with embedding generation, all API calls must be designed to operate within the free tier's rate limits.30 The token limits (TPM/TPD) become particularly relevant here, as generating extensive text can consume tokens rapidly.44
The successful integration of AI features hinges on managing these API interactions carefully. The "free" nature of these AI capabilities is tied to modest usage patterns; any significant scaling would quickly necessitate moving to paid tiers of the Gemini API, which offer higher rate limits.30


7. Firebase for Future Mobile Support


A key consideration in the migration strategy is the potential for future mobile application development. Leveraging Firebase services from the outset for the web application provides a strong foundation for such an expansion.


Overview of Firebase Services Beneficial for Mobile Apps


Firebase is a comprehensive platform designed to accelerate mobile and web application development. Several of the services already selected for this web application migration—namely Firestore and Firebase Authentication—are core Firebase offerings that integrate seamlessly with native mobile SDKs for iOS and Android.16 Firestore, for example, offers features like real-time data synchronization and offline data persistence, which are highly valuable for creating responsive and engaging mobile experiences.16
Beyond these, Firebase offers a suite of other services that are particularly beneficial for mobile applications, many of which have generous free tiers or are entirely free:
                                 * Cloud Functions for Firebase: While Cloud Run is the primary compute choice for the Rails backend, Cloud Functions for Firebase can be useful for event-driven backend code triggered by Firebase events (e.g., Firestore writes, new user sign-ups). It has a free tier of 2 million invocations per month, plus allowances for compute time and outbound networking.13
                                 * Firebase Cloud Messaging (FCM): For sending push notifications to mobile devices, FCM is a robust and free service.13
                                 * Crashlytics: Provides real-time crash reporting for mobile apps, helping to identify and fix stability issues. This service is free.13
                                 * Google Analytics for Firebase: Offers free and unlimited analytics to understand user behavior within mobile apps.13


Alignment with Current Choices


The architectural decisions made for the web application—specifically adopting Firestore for the database and Firebase Authentication for user management—directly support and simplify future mobile app development. Firebase provides client-side SDKs for popular mobile platforms (iOS, Android, Flutter, Unity) that interact directly with Firestore and Firebase Authentication.16 This means that much of the backend infrastructure, data storage, and authentication logic established for the web application can be reused by a mobile counterpart, significantly reducing development time and effort for a mobile launch.
By building the web application on these Firebase-centric services, the backend is inherently mobile-ready. This strategic alignment offers potential long-term cost and time savings should the project expand to include native mobile applications, as a unified backend can serve both web and mobile clients efficiently.


8. Managing Free Tier Limits and Avoiding Costs


Successfully operating the application within the GCP free tier requires diligent management of resource consumption and a clear understanding of how various limits apply, particularly concerning data egress and operational quotas. The primary goal is to avoid any action that necessitates enabling billing on the Google Cloud project, as this opens the possibility of incurring charges if free tier limits are exceeded on any service.


8.1. Data Egress


Network egress (data leaving Google Cloud) is a common source of cloud costs. Several services in the proposed architecture have their own free egress allowances:
                                 * Cloud Run: The free tier includes 1 GiB of outbound internet data transfer from North America per month.2
                                 * Firestore: Provides 10 GiB of free outbound data transfer per month.2 This typically applies to data read from Firestore and sent to clients outside GCP or to other GCP regions.
                                 * Firebase Hosting: When used for serving static assets or as a proxy for Cloud Run, Firebase Hosting offers 10 GB of storage and 360 MB/day of data transfer (approximately 10.8 GB/month) within its free tier.13
                                 * General GCP Egress: The broader Google Cloud free tier also includes 1 GiB of network egress from North America to all region destinations (excluding China and Australia) per month, per billing account.1
Aggregation and Intra-Region Traffic:
Free tier usage, including egress, is generally aggregated across projects by billing account and resets monthly.5 It is important to understand how these allowances interact. For instance, the 1 GiB from Cloud Run and 10 GiB from Firestore are specific to those services. A crucial cost-saving factor is that data transfer
within the same GCP region between most Google Cloud services (e.g., from Cloud Run to Firestore, or Cloud Run to Gemini API endpoints within the same region) is typically free.5 Architecting the application to keep inter-service communication within the same region is therefore highly recommended. For an application with no users, exceeding these combined egress limits is unlikely if data is primarily exchanged between GCP services in the same region and minimal data is served to the public internet.


8.2. Monitoring Usage


Continuous monitoring is essential to ensure the application remains within the free tier limits.
                                 * Google Cloud Console: The GCP Console provides tools for tracking resource consumption. The Billing reports, Quotas pages, and service-specific dashboards (e.g., for Cloud Run, Firestore) should be regularly reviewed.2
                                 * Firebase Console: The Firebase Console also offers usage tabs for Firebase-specific services like Authentication, Firestore (usage often mirrors GCP console), and Hosting.3
                                 * Budget Alerts: Although the goal is to avoid enabling billing, if it were ever enabled for any reason (e.g., to access a paid-tier feature temporarily), setting up budget alerts in GCP Billing is crucial to get notified before spending exceeds predefined thresholds.18


8.3. Strategies for Handling Potential Overages


The primary strategy is prevention. This involves:
                                 1. Designing for the Free Tier: Architecting the application from the ground up with free tier limitations in mind (e.g., optimizing Firestore queries, minimizing data storage, batching API calls).
                                 2. Not Enabling Billing: If billing is not enabled on the GCP project, services will typically cease to function or return errors upon exceeding their hard free quotas, rather than accruing charges.9 This acts as a hard stop.
                                 3. Regular Monitoring: As detailed above, keeping a close eye on usage dashboards.
If, hypothetically, billing were enabled and overages occurred, the only recourse would be to identify the source of the overage and adjust application behavior or resource configuration to bring usage back within free limits, or accept the costs. The strict "zero-cost" requirement for this project phase means that enabling billing is to be avoided.
The interconnectedness of services means that usage in one area can impact another. For example, a high volume of requests to a Cloud Run service (even if within its free 2 million requests) could lead to a high volume of Firestore reads, potentially exceeding Firestore's free daily read quota. This underscores the need for a holistic view of resource consumption. The "zero-cost" goal is not merely about selecting services that offer free tiers, but about ensuring the application's operational patterns consistently stay below the thresholds where billing enablement becomes a prerequisite for functionality or where charges would begin if billing were active.


9. Revised Research Plan & Key Considerations


To effectively migrate the Ruby on Rails application to GCP's free tier while meeting all specified requirements, the following step-by-step research and prototyping tasks are recommended. These tasks are designed to validate assumptions, identify potential challenges early, and ensure the final architecture aligns with the zero-cost objective.


Step-by-step Research Tasks:


                                 1. Task 1: Confirm Firestore kNN Index Read Counting (Critical Priority)
                                 * Objective: Obtain explicit confirmation from Google Cloud regarding how K-nearest neighbor (kNN) vector index reads are counted towards Firestore's 50,000 free daily document read operations.
                                 * Method: Consult official Google Cloud SKU documentation in detail and, if ambiguity persists, contact Google Cloud Support or seek clarification through official community channels.
                                 * Rationale: This is the most significant potential cost variable. If each batch of 100 kNN index entries scanned counts as one standard document read from the free quota, vector search usage could rapidly deplete free allowances.15 Understanding this is paramount for cost feasibility.
                                 2. Task 2: Prototype Ruby on Rails on Cloud Run with Custom Domain
                                 * Objective: Deploy a minimal Ruby on Rails application (web server and a basic background worker) to Cloud Run. Configure and test custom domain access via Firebase Hosting.
                                 * Method:
                                 * Containerize a simple Rails app.
                                 * Deploy one Cloud Run service for the web component (request-based billing) and one Cloud Run Job for a simulated background task (instance-based billing).5
                                 * Configure Firebase Hosting to proxy requests to the Cloud Run web service, including setting up the custom domain and SSL.12
                                 * Rationale: Validates the core compute and networking setup, and familiarizes with deployment processes for RoR on Cloud Run.
                                 3. Task 3: Implement Firebase Authentication in Rails
                                 * Objective: Prototype user registration and login flows using Firebase Authentication on the client-side, and implement ID token verification on the Rails backend. Link Firebase UIDs to user records in Firestore.
                                 * Method:
                                 * Use Firebase SDKs on a simple web client for sign-up/sign-in.
                                 * In the Rails backend, implement ID token verification using a Ruby JWT library (e.g., ruby-jwt).36 This will involve fetching and caching Google's public signing keys.37
                                 * Upon successful verification, create/retrieve a user document in Firestore using the Firebase UID as the document ID.
                                 * Rationale: Addresses the authentication migration and establishes the link between Firebase Auth users and Firestore data, a critical part of the architecture.35
                                 4. Task 4: Design Firestore Data Model for Users, Content, and Vector Search
                                 * Objective: Develop a detailed Firestore data model for user profiles and application content, explicitly including fields for storing vector embeddings. Plan denormalization strategies from the existing PostgreSQL schema.
                                 * Method: Analyze existing PostgreSQL schema. Design Firestore collections (e.g., users, content_items) and document structures, considering query patterns and vector search requirements.16
                                 * Rationale: A well-designed NoSQL schema is crucial for performance and cost-efficiency in Firestore, especially with vector search.
                                 5. Task 5: Prototype Data Migration: Embedding Generation and Firestore Import
                                 * Objective: Develop and test a Ruby script to take sample data (simulating export from PostgreSQL), generate vector embeddings using a free-tier Gemini API model, and batch-write the data (including embeddings) to Firestore.
                                 * Method:
                                 * Create sample JSON data.
                                 * Write a Ruby script to call the Gemini API for embeddings, respecting rate limits (e.g., 5 RPM for Gemini Embedding Experimental).30 Implement delays and error handling.
                                 * Use the google-cloud-firestore Ruby gem to perform batch writes to Firestore.33
                                 * Monitor Firestore write operations against the 20,000/day free limit.4
                                 * Rationale: Tests the most complex part of the data migration pipeline and provides insights into the practicalities of rate limiting and batch processing.
                                 6. Task 6: Prototype Vector Search Queries in Firestore
                                 * Objective: Implement and test sample kNN vector search queries against the data imported into Firestore.
                                 * Method: Using a supported client library (or REST API if Ruby SDK lacks direct support), execute vector search queries. Analyze the number of document reads and, crucially, index entries scanned to understand potential cost implications based on findings from Task 1.
                                 * Rationale: Validates the vector search functionality and provides data on its operational cost within the free tier.15
                                 7. Task 7: Test Basic AI Features with Gemini Text Generation Models
                                 * Objective: Implement a simple AI-driven text generation feature (e.g., content summarization or description generation) using a free-tier Gemini model like Gemini 1.5 Flash.
                                 * Method: Call the Gemini API from the Rails backend for a text generation task, ensuring adherence to RPM/TPM rate limits.30
                                 * Rationale: Confirms the ability to integrate other AI functionalities within free tier constraints.
                                 8. Task 8: Simulated Stress Test and Comprehensive Monitoring
                                 * Objective: Simulate low levels of traffic and background job execution to monitor overall resource consumption against all relevant free tier limits.
                                 * Method: Use GCP and Firebase console dashboards to track Cloud Run vCPU/memory/requests, Firestore reads/writes/storage/egress, Firebase Authentication MAU, and Gemini API calls.
                                 * Rationale: Provides a holistic view of resource usage and helps identify any unexpected bottlenecks or services approaching free limits.


Critical Decision Points and Trade-offs:


                                 * Firestore Vector Search Cost Impact: The outcome of Task 1 is pivotal. If kNN index reads significantly consume the free daily document read quota, the viability of native Firestore vector search for a zero-cost application may be compromised. Alternatives might include performing vector calculations client-side for very small datasets (not scalable), or forgoing vector search until the application moves to a paid tier.
                                 * Background Job Implementation: The primary recommendation is Cloud Run Jobs. However, if challenges arise, alternative patterns like a continuously running low-resource Cloud Run service polling a simple queue (e.g., a Firestore collection acting as a queue, though this incurs read/write costs) or using App Engine Task Queues (if App Engine were chosen) could be re-evaluated. Pub/Sub triggered Cloud Run services are also an option, though Pub/Sub itself has a free tier (10 GB messages/month) but adds another service to manage.1
                                 * Complexity of Manual Token Verification in Rails: If implementing secure Firebase ID token verification with key caching in Ruby proves overly complex or raises security concerns, this might warrant re-evaluation. However, given the "zero-cost" goal, alternative free, robust authentication services are scarce.


Risk Assessment for Staying Within Free Tier:


                                 * Primary Risk: Firestore Operational Costs. Exceeding daily read/write/delete quotas or incurring significant costs from vector search index reads (if they are not generously covered by the free document read quota) is the highest risk. This is mitigated by careful data modeling, query optimization, and the findings of Task 1.
                                 * Secondary Risk: Gemini API Rate Limits. During initial data migration (embedding generation) or if future AI features become frequently used even by a few test users, hitting RPM/TPM/RPD limits could stall operations or require extensive retry logic.
                                 * Tertiary Risk: Network Egress Charges. While individual services have egress allowances, and intra-region traffic is mostly free, misconfiguration or unexpected external data flows could lead to egress charges if the overall GCP free egress limit is exceeded. This is a lower risk for a no-user application but requires awareness.
                                 * Quaternary Risk: Changes to Free Tiers. Google Cloud's free tier offerings can change over time.1 While this plan is based on current offerings, long-term cost-free operation is subject to these policies remaining stable.
This research plan is iterative. Findings from earlier tasks, particularly the confirmation of Firestore vector search costs, will directly inform the design and feasibility of subsequent tasks. Successfully navigating the GCP free tier for a non-trivial application is an investment in developer time for research, prototyping, and meticulous design, even if direct service costs are zero.


10. Conclusion and Recommendations




Summary of Viable Path


The migration of the Ruby on Rails application from Render to the Google Cloud Platform free tier, with the goal of zero ongoing monetary cost, appears feasible. The recommended architecture involves:
                                 * Compute: Cloud Run for both the web server (request-based billing, scaling to zero) and background job worker (instance-based billing via Cloud Run Jobs).
                                 * Database: Firestore (Native Mode) for data storage, leveraging its native vector search capabilities.
                                 * Authentication: Firebase Authentication (Spark Plan) to replace Devise.
                                 * AI Features: Gemini API models (e.g., embedding models, Gemini 1.5 Flash) utilized within their free tier rate limits.
                                 * Custom Domain: Firebase Hosting serving as a proxy to the Cloud Run web service, providing SSL and CDN benefits within its free tier.


Confirmation of Zero-Cost Feasibility


Achieving zero ongoing monetary costs is possible provided the application operates strictly within the defined free tier limits of each service and, crucially, if the cost implications of Firestore's native vector search (specifically, how kNN index reads are counted against free document read quotas) are favorable or can be managed through careful design. The current "no users" status of the application significantly aids in staying within these limits.


Key Success Factors


Maintaining a zero-cost footprint will depend on:
                                 1. Confirmation of Firestore Vector Search Costs: Prioritizing research (Task 1 in the plan) to clarify how kNN index reads affect the free daily document read quota is paramount.
                                 2. Disciplined Development: Designing all features, especially data queries and AI interactions, with free tier limitations as hard constraints.
                                 3. Continuous Monitoring: Regularly using GCP and Firebase console dashboards to track resource consumption against quotas.
                                 4. Avoiding Billing Enablement: Strictly avoiding features or service levels that require enabling billing on the GCP project, as this introduces the risk of unintended charges.
                                 5. Efficient Data Migration: Carefully batching and rate-limiting processes like initial data embedding generation to comply with Gemini API and Firestore write limits.


Final Caveats


It is essential to recognize that Google Cloud's free tier offerings, while generous for development and low-traffic applications, can be subject to change over time.1 Furthermore, as the application evolves, gains users, or requires more sophisticated features or higher performance, a transition to paid tiers will inevitably become necessary. The proposed architecture, however, provides a solid foundation on scalable services, making such a transition manageable when the time comes. The "free tier" is a strategic enabler for the current development phase, not a permanent solution for a production application experiencing growth. Investing the developer time to understand and effectively utilize these free tiers is a valuable endeavor, fostering cost-efficient innovation and experimentation.
Источники
                                 1. Free Trial and Free Tier Services and Products - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/free
                                 2. Free cloud features and trial offer | Google Cloud Free Program, дата последнего обращения: мая 22, 2025, https://cloud.google.com/free/docs/free-cloud-features
                                 3. Usage and limits | Firestore - Firebase - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/firestore/quotas
                                 4. Understand Cloud Firestore billing | Firebase, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/firestore/pricing
                                 5. Cloud Run pricing, дата последнего обращения: мая 22, 2025, https://cloud.google.com/run/pricing
                                 6. Running Rails on the Cloud Run environment | Ruby - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/ruby/rails/run
                                 7. Quickstart: Build and deploy a Ruby web app to Google Cloud with Cloud Run, дата последнего обращения: мая 22, 2025, https://cloud.google.com/run/docs/quickstarts/build-and-deploy/deploy-ruby-service
                                 8. Laravel vs Django and Rails for SaaS Development - Vincent Schmalbach, дата последнего обращения: мая 22, 2025, https://www.vincentschmalbach.com/laravel-vs-django-and-rails-for-saas-development/
                                 9. Quotas and limits | Google App Engine standard environment docs ..., дата последнего обращения: мая 22, 2025, https://cloud.google.com/appengine/docs/standard/quotas
                                 10. How to Best Leverage the GCP Free Tier - Cobry, дата последнего обращения: мая 22, 2025, https://www.cobry.co.uk/gcp-free-tier
                                 11. Create jobs | Cloud Run Documentation, дата последнего обращения: мая 22, 2025, https://cloud.google.com/run/docs/create-jobs
                                 12. Mapping custom domains | Cloud Run Documentation | Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/run/docs/mapping-custom-domains
                                 13. Firebase Pricing - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/pricing
                                 14. Registering a domain name while using free trial of Google Cloud, дата последнего обращения: мая 22, 2025, https://support.google.com/a/thread/321597395/registering-a-domain-name-while-using-free-trial-of-google-cloud?hl=en
                                 15. Pricing | Firestore | Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/firestore/pricing
                                 16. Firestore | Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/products/firestore
                                 17. Get started with generative AI | Firestore in Native mode - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/firestore/native/docs/solutions/generative-ai-index
                                 18. Google Firestore Pricing Guide: Real-World Costs & Optimization Tips | Airbyte, дата последнего обращения: мая 22, 2025, https://airbyte.com/data-engineering-resources/google-firestore-pricing
                                 19. Search with vector embeddings | Firestore - Firebase - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/firestore/vector-search
                                 20. Search with vector embeddings | Firestore in Native mode - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/firestore/native/docs/vector-search
                                 21. Structure data | Firestore in Native mode - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/firestore/native/docs/concepts/structure-data
                                 22. Tutorial: Firestore NoSQL Relational Data Modeling | Fireship.io, дата последнего обращения: мая 22, 2025, https://fireship.io/lessons/firestore-nosql-data-modeling-by-example/
                                 23. Best practices for Cloud Firestore - Firebase, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/firestore/best-practices
                                 24. Firestore Vector Full-Text Search - Code.Build, дата последнего обращения: мая 22, 2025, https://code.build/p/firestore-vector-full-text-search-Gli7vK
                                 25. Migrate from Firebase Firestore to Neon Postgres - Neon Docs, дата последнего обращения: мая 22, 2025, https://neon.tech/docs/import/migrate-from-firebase
                                 26. ruby-pg/ext/pg_result.c at master - GitHub, дата последнего обращения: мая 22, 2025, https://github.com/ged/ruby-pg/blob/master/ext/pg_result.c
                                 27. iterate over all values of a pg_conn query in Ruby - Stack Overflow, дата последнего обращения: мая 22, 2025, https://stackoverflow.com/questions/15903719/iterate-over-all-values-of-a-pg-conn-query-in-ruby
                                 28. How to work with JSON data in Ruby? - CloudDevs, дата последнего обращения: мая 22, 2025, https://clouddevs.com/ruby/json-data/
                                 29. How to write to a JSON file in the correct format - Stack Overflow, дата последнего обращения: мая 22, 2025, https://stackoverflow.com/questions/5507512/how-to-write-to-a-json-file-in-the-correct-format
                                 30. Rate limits | Gemini API | Google AI for Developers, дата последнего обращения: мая 22, 2025, https://ai.google.dev/gemini-api/docs/rate-limits
                                 31. Get text embeddings | Generative AI on Vertex AI - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/vertex-ai/generative-ai/docs/embeddings/get-text-embeddings
                                 32. Usage limits | Google Drive, дата последнего обращения: мая 22, 2025, https://developers.google.com/workspace/drive/api/guides/limits
                                 33. Quickstart: Create a Firestore database by using a server client library - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/firestore/native/docs/create-database-server-client-library
                                 34. 2025 Firebase Authentication's latest pricing explained and the best alternatives, дата последнего обращения: мая 22, 2025, https://blog.logto.io/firebase-authentication-pricing
                                 35. Firebase Authentication Limits - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/auth/limits
                                 36. Verify ID Tokens | Firebase Authentication - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/auth/admin/verify-id-tokens
                                 37. How to verify firebase ID token with PHP(JWT)? - Stack Overflow, дата последнего обращения: мая 22, 2025, https://stackoverflow.com/questions/42098150/how-to-verify-firebase-id-token-with-phpjwt
                                 38. fschuindt/firebase_id_token: A Ruby gem to verify the signature of Firebase ID Tokens. - GitHub, дата последнего обращения: мая 22, 2025, https://github.com/fschuindt/firebase_id_token
                                 39. Manage User Sessions | Firebase Authentication - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/auth/admin/manage-sessions
                                 40. Manage Session Cookies - Authentication - Firebase, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/auth/admin/manage-cookies
                                 41. Embeddings | Gemini API | Google AI for Developers, дата последнего обращения: мая 22, 2025, https://ai.google.dev/gemini-api/docs/embeddings
                                 42. Learn about supported models | Vertex AI in Firebase - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/vertex-ai/models
                                 43. Gemini Developer API Pricing | Gemini API | Google AI for Developers, дата последнего обращения: мая 22, 2025, https://ai.google.dev/gemini-api/docs/pricing
                                 44. Count tokens and billable characters for Gemini models | Firebase AI Logic - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/ai-logic/count-tokens
                                 45. дата последнего обращения: декабря 31, 1969, https://ai.google.dev/gemini-api/docs/introduction
                                 46. CountTokens API | Generative AI on Vertex AI - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/count-tokens
                                 47. Generate text using the Gemini API | Vertex AI in Firebase - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/vertex-ai/generate-text
                                 48. Firebase Cloud Functions Explained for beginners [intro, examples, pricing] - YouTube, дата последнего обращения: мая 22, 2025, https://www.youtube.com/watch?v=mvQP0Dlgo2A
                                 49. Firebase pricing plans, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/projects/billing/firebase-pricing-plans
                                 50. Google Cloud Pricing: The Complete Guide - NetApp, дата последнего обращения: мая 22, 2025, https://www.netapp.com/blog/gcp-cvo-blg-google-cloud-pricing-the-complete-guide/
                                 51. Transactions and batched writes | Firestore - Firebase - Google, дата последнего обращения: мая 22, 2025, https://firebase.google.com/docs/firestore/manage-data/transactions
                                 52. Use Pub/Sub with Cloud Run tutorial - Google Cloud, дата последнего обращения: мая 22, 2025, https://cloud.google.com/run/docs/tutorials/pubsub
                                 53. Trigger functions from Pub/Sub using Eventarc | Cloud Run ..., дата последнего обращения: мая 22, 2025, https://cloud.google.com/run/docs/tutorials/pubsub-eventdriven
                                 54. Quotas for Google Services | Apps Script, дата последнего обращения: мая 22, 2025, https://developers.google.com/apps-script/guides/services/quotas