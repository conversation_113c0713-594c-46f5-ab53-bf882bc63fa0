#!/usr/bin/env ruby
# frozen_string_literal: true

require 'fileutils'

# path to your application root.
APP_ROOT = File.expand_path('..', __dir__)

def system!(*)
  system(*, exception: true)
end

FileUtils.chdir APP_ROOT do
  # This script is a way to set up or update your development environment automatically.
  # This script is idempotent, so that you can run it at any time and get an expectable outcome.
  # Add necessary setup steps to this file.
  if `cat /etc/passwd`.include?('gitpod')
    puts 'updating db permissions'
    system! 'sudo adduser gitpod dev'
    system! 'sudo chown gitpod $PGDATA'
    system! 'initdb -D $PGDATA'
    system! 'pg_ctl start'
  end

  puts '== Installing dependencies =='
  system!('bundle install')

  # puts "\n== Copying sample files =="
  # unless File.exist?("config/database.yml")
  #   FileUtils.cp "config/database.yml.sample", "config/database.yml"
  # end

  puts "\n== Preparing database =="
  system! 'bin/rails db:create db:schema:load db:seed'

  puts "\n== Removing old logs and tempfiles =="
  system! 'bin/rails log:clear tmp:clear'

  puts "\n== Restarting application server =="
  system! 'bin/rails restart'

  puts "\n== Creating test database =="
  system! 'bundle exec rake db:create RAILS_ENV=test'
  puts "\n== Initial setup complete =="
end
