# PostgreSQL. Versions 9.3 and up are supported.
#
# Install the pg driver:
#   gem install pg
# On macOS with Homebrew:
#   gem install pg -- --with-pg-config=/usr/local/bin/pg_config
# On macOS with MacPorts:
#   gem install pg -- --with-pg-config=/opt/local/lib/postgresql84/bin/pg_config
# On Windows:
#   gem install pg
#       Choose the win32 build.
#       Install PostgreSQL and put its /bin directory on your path.
#
# Configure Using Gemfile
# gem "pg"
#
default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  # Add connection timeout and statement timeout for better resource management
  connect_timeout: 5
  checkout_timeout: 5
  variables:
    statement_timeout: <%= ENV.fetch("STATEMENT_TIMEOUT") { "5000" } %>

development:
  <<: *default
  database: cinematch_development
  host: localhost
  port: 5433
  username: student

test:
  <<: *default
  database: cinematch_test
  host: localhost
  port: 5433
  username: student

# As with config/credentials.yml, you never want to store sensitive information,
# like your database password, in your source code. If your source code is
# ever seen by anyone, they now have access to your database.
#
# Instead, provide the password or a full connection URL as an environment
# variable when you boot the app. For example:
#
#   DATABASE_URL="postgres://myuser:mypass@localhost/somedatabase"
#
# If the connection URL is provided in the special DATABASE_URL environment
# variable, Rails will automatically merge its configuration values on top of
# the values provided in this file. Alternatively, you can specify a connection
# URL environment variable explicitly:
#
#   production:
#     url: <%= ENV["MY_APP_DATABASE_URL"] %>
#
# Read https://guides.rubyonrails.org/configuring.html#configuring-a-database
# for a full overview on how database connection configuration can be specified.
#
production:
  <<: *default
  url: <%= ENV['DATABASE_URL'] %>
  # Optimize connection pool for production
  pool: <%= ENV.fetch("DB_POOL") { 10 } %>
  # Add connection timeout and statement timeout for better resource management
  connect_timeout: 5
  checkout_timeout: 5
  variables:
    statement_timeout: <%= ENV.fetch("STATEMENT_TIMEOUT") { "10000" } %>

job_runner:
  <<: *default
  url: <%= ENV['DATABASE_URL'] %>
  pool: <%= ENV.fetch("DB_POOL") { 20 } %>
  # Add connection timeout and statement timeout for better resource management
  connect_timeout: 10
  checkout_timeout: 10
  variables:
    statement_timeout: <%= ENV.fetch("STATEMENT_TIMEOUT") { "30000" } %>
