# Cloud Build configuration for building and deploying the job runner to Cloud Run

# Set environment variables for the build
substitutions:
  _SERVICE_NAME: cinematch-jobs
  _REGION: us-central1
  _PLATFORM: managed

# Build the Docker image
steps:
  # Build the container image (same as web, but with a different entrypoint)
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA', '--target=development', '.']
    id: 'Build'

  # Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA']
    id: 'Push'

  # Deploy container image to Cloud Run with concurrency=1 to ensure jobs don't overlap
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - '${_PLATFORM}'
      - '--no-allow-unauthenticated'
      - '--set-env-vars=RAILS_ENV=production,GOOD_JOB_EXECUTION_MODE=async,RAILS_LOG_TO_STDOUT=true'
      - '--add-cloudsql-instances=$PROJECT_ID:${_REGION}:cinematch-db'
      - '--set-secrets=DATABASE_URL=projects/$PROJECT_ID/secrets/DATABASE_URL:latest'
      - '--max-instances=1'
      - '--cpu=1'
      - '--memory=1Gi'
      - '--concurrency=1'
    id: 'Deploy'

# Store images in Artifact Registry
images:
  - 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA'
