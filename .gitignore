# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep

/public/assets

# Ignore master key for decrypting credentials and more.
/config/master.key
# AppDev files
core.chrome*
examples.txt
.vscode/.ltici_apitoken.yml

# Ignore dotenv files
/.env*
.env

# Un-ignore SQLite3 db
!/db/*.sqlite3
!/db/*.sqlite3-journal

# Ignore PostgreSQL backup files
cinematch_backup.sql
postgresql.conf.backup
pg_hba.conf.backup
