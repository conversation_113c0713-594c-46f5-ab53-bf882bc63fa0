# frozen_string_literal: true

source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.2.1"
gem "activeadmin"
gem "acts_as_list"
gem "anthropic", "~> 0.3.2"
gem "appdev_support"
gem "awesome_print"
gem "bootsnap", require: false
gem "clamav-client"
gem "csso-rails"
gem "devise"
gem "devise-security"
gem "devise-two-factor"
gem "dotenv-rails"
gem "email_validator"
gem "faker"
gem "get_process_mem"
gem "good_job"
gem "htmlbeautifier"
gem "http"
gem "httparty"
gem "importmap-rails"
gem "jbuilder"
gem "jquery-rails"
gem "kaminari"
gem "omniauth"
gem "omniauth-apple"
gem "omniauth-google-oauth2"
gem "omniauth-facebook"
gem "omniauth-rails_csrf_protection"
gem "omniauth-twitter"
gem "openssl", "~> 3.2.0"
gem "parallel"
gem "paranoia", "~> 2.6"
gem "pg", "~> 1.1"
gem "prawn"
gem "prawn-table"
gem "puma"
gem "pundit"
gem "rack-attack"
gem "rails", "~> 7.1.3", ">= 7.1.3.2"
gem "recaptcha"
gem "redis", "~> 4.8"
gem "redis-namespace", "~> 1.10"
gem "redis-rails", "~> 5.0"
gem "rexml", "~> 3.3.6"
gem "ruby-openai"
gem "sassc-rails"
gem "sitemap_generator"
gem "sprockets-rails"
gem "sqlite3", "~> 1.4"
gem "stimulus-rails"
gem "table_print"
gem "turbolinks", "~> 5"
gem "turbo-rails"
gem "tzinfo-data", platforms: %i[mingw mswin x64_mingw jruby]
gem "whenever"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[mri mingw x64_mingw]
  gem "rspec-rails", "~> 6.0.0"
  gem "rubocop", require: false
  gem "factory_bot_rails"
  gem "shoulda-matchers"
  gem "database_cleaner"
end

group :development do
  gem "annotate"
  gem "better_errors"
  gem "binding_of_caller"
  gem "draft_generators"
  gem "grade_runner"
  gem "pry-rails"
  gem "rails_db"
  gem "rails-erd"
  gem "rufo"
  gem "specs_to_readme"
  gem "web-console"

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara"
  gem "draft_matchers"
  gem "rspec-html-matchers"
  gem "selenium-webdriver"
  gem "webdrivers"
  gem "webmock"
end
