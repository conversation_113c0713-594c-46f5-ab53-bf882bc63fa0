# Ignore all dotfiles and dot directories except those we need
.*
!.dockerignore
!.gitignore

# Ignore node_modules
node_modules

# Ignore log files and temp files
log/*
tmp/*
!/log/.keep
!/tmp/.keep

# Ignore test and development files
test/
spec/
coverage/

# Ignore environment files that might contain secrets
.env*
!.env.example

# Ignore IDE and editor files
.idea/
.vscode/
*.swp
*.swo

# Ignore Docker related files
Dockerfile*
docker-compose*
.dockerignore

# Ignore production files
/public/assets
/public/packs
/public/packs-test
/public/system

# Ignore yarn files
yarn-debug.log*
yarn-error.log*

# Ignore OS specific files
.DS_Store
Thumbs.db
