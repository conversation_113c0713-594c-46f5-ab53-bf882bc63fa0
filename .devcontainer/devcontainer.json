// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/universal
{
  "name": "Container",
  // Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
  "image": "firstdraft/appdev-rails-template",

  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  "forwardPorts": [3000, 4567, 9292],

  "portsAttributes": {
    "3000": {
      "onAutoForward": "silent"
    },
    "4567": {
      "onAutoForward": "silent"
    },
    "9292": {
      "onAutoForward": "silent"
    }
  },

  "otherPortsAttributes": {"onAutoForward": "ignore"},

  // Use 'postCreateCommand' to run commands after the container is created.
  "postCreateCommand": "bin/setup",

  // Configure tool-specific properties.
  "customizations": {
    "vscode": {
      "extensions": ["vortizhe.simple-ruby-erb",
                     "mbessey.vscode-rufo",
                     "aliariff.vscode-erb-beautify",
                     "eamodio.gitlens",
                     "setobiralo.erb-commenter"]
    }
  }

  // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "root"
}
