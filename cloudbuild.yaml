# Cloud Build configuration for building and deploying to Cloud Run

# Set environment variables for the build
substitutions:
  _SERVICE_NAME: cinematch-web
  _REGION: us-central1
  _PLATFORM: managed

# Build the Docker image
steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA', '.']
    id: 'Build'

  # Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA']
    id: 'Push'

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - '${_PLATFORM}'
      - '--allow-unauthenticated'
      - '--set-env-vars=RAILS_ENV=production,RAILS_SERVE_STATIC_FILES=true,RAILS_LOG_TO_STDOUT=true'
      - '--add-cloudsql-instances=$PROJECT_ID:${_REGION}:cinematch-db'
      - '--set-secrets=DATABASE_URL=projects/$PROJECT_ID/secrets/DATABASE_URL:latest'
    id: 'Deploy'

# Store images in Artifact Registry
images:
  - 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA'
