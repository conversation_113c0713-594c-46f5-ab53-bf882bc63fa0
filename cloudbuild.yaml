# Cloud Build configuration for building and deploying to Cloud Run

# Set environment variables for the build
substitutions:
  _SERVICE_NAME: cinematch-web
  _REGION: us-central1
  _PLATFORM: managed

# Build the Docker image
steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:$BUILD_ID', '.']
    id: 'Build'

  # Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:$BUILD_ID']
    id: 'Push'

  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - '${_SERVICE_NAME}'
      - '--image'
      - 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:$BUILD_ID'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - '${_PLATFORM}'
      - '--allow-unauthenticated'
      - '--set-env-vars=RAILS_ENV=production,RAILS_SERVE_STATIC_FILES=true,RAILS_LOG_TO_STDOUT=true,SKIP_REDIS=true'
      - '--set-secrets=DATABASE_URL=db-url:latest,SECRET_KEY_BASE=secret-key-base:latest,ANTHROPIC_API_KEY=anthropic-api-key:latest,OPENAI_API_KEY=openai-api-key:latest,THEMOVIEDB_KEY=themoviedb-key:latest'
      - '--cpu=1'
      - '--memory=512Mi'
      - '--concurrency=1'
      - '--max-instances=1'
    id: 'Deploy'

# Store images in Artifact Registry
images:
  - 'us-central1-docker.pkg.dev/$PROJECT_ID/cinematch-repo/${_SERVICE_NAME}:$BUILD_ID'
