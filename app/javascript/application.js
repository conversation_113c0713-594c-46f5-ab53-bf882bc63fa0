import "@hotwired/turbo-rails"
import "controllers"
import "survey"
console.log('Survey module loaded');

// import * as bootstrap from "bootstrap"
// window.bootstrap = bootstrap

import "chart.js"
import "./pwa/companion"

// Import Bootstrap JS components if needed (or rely on CDN bundle)
// import { Tooltip } from 'bootstrap' 

// Standard Stimulus setup with importmaps
// import { Application } from "@hotwired/stimulus"
// window.Stimulus = Application.start() 
// NOTE: The import "controllers" line above handles loading controllers 
// located in app/javascript/controllers/index.js (or generated by rails)

// Remove legacy Stimulus setup
// const context = require.context("./controllers", true, /\.js$/)
// Stimulus.load(definitionsFromContext(context))


document.addEventListener('DOMContentLoaded', () => {
  addCSRFTokenToForms();
  // Remove legacy watchlist initialization - handled by Stimulus auto-loading
  // initializeWatchlistControllers(); 
});

function addCSRFTokenToForms() {
  var token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
  document.querySelectorAll('form').forEach(function(form) {
    var input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'authenticity_token';
    input.value = token;
    form.appendChild(input);
  });
}

// Remove legacy watchlist initialization function
// function initializeWatchlistControllers() {
//   const watchlistToggles = document.querySelectorAll('[data-controller="watchlist"]');
//   watchlistToggles.forEach(toggle => {
//     // This manual loading isn't typically needed with import "controllers"
//     // window.Stimulus.load(toggle); 
//   });
// }

// Remove export, Stimulus is typically globally available via window.Stimulus if needed
// export { Stimulus }


document.addEventListener('DOMContentLoaded', function() {
  $(document).ajaxError(function(event, xhr, settings) {
    if (xhr.status === 401) {
      window.location.href = '/users/sign_in';
    }
  });

  $(document).ajaxSuccess(function(event, xhr, settings) {
    if (xhr.responseJSON && xhr.responseJSON.redirect) {
      window.location.href = xhr.responseJSON.redirect;
    }
  });
});

// Remove redundant registerControllers call
// registerControllers(Stimulus)
