<title><%= content_for?(:title) ? yield(:title) + " | Cinematch" : "Cinematch - Personalized Movie and TV Show Recommendations" %></title>
<meta name="description" content="<%= content_for?(:description) ? yield(:description) : "Discover films and series that match your unique personality. Cinematch uses psychological insights to suggest content tailored to who you are." %>">
<!-- Add canonical URL -->
<link rel="canonical" href="<%= request.original_url %>" />
    
<!-- Add Open Graph meta tags -->
<meta property="og:title" content="<%= content_for?(:title) ? yield(:title) + " | Cinematch" : "Cinematch - Personalized Movie and TV Show Recommendations" %>">
<meta property="og:description" content="<%= content_for?(:description) ? yield(:description) : "Discover films and series that match your unique personality. Cinematch uses psychological insights to suggest content tailored to who you are." %>">
<meta property="og:image" content="<%= asset_url("logos/cm_meta_glare_1200.png") %>">
<meta property="og:url" content="<%= request.original_url %>">
<meta property="og:type" content="website">
    
<!-- Add Twitter Card meta tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:url" content="<%= request.original_url %>">
<meta name="twitter:title" content="<%= content_for?(:title) ? yield(:title) + " | Cinematch" : "Cinematch - Personalized Movie and TV Show Recommendations" %>">
<meta name="twitter:description" content="<%= content_for?(:description) ? yield(:description) : "Discover films and series that match your unique personality. Cinematch uses psychological insights to suggest content tailored to who you are." %>">
<meta name="twitter:image" content="<%= asset_url("logos/cm_meta_glare_1200.png") %>">

<!-- Add PWA Support -->
<link rel="apple-touch-icon" sizes="180x180" href="<%= image_path 'logos/cm_icon_sq.png' %>">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-title" content="Cinematch">
<link rel="icon" type="image/png" sizes="32x32" href="<%= image_path 'logos/cm_icon_sq.png' %>">
<link rel="icon" type="image/png" sizes="16x16" href="<%= image_path 'logos/cm_icon_sq.png' %>">
<link rel="manifest" href="/manifest.json">
<link rel="mask-icon" href="<%= image_path 'logos/cm_icon_sq.svg' %>" color="#2b2d42">
<meta name="msapplication-TileImage" content="<%= image_path 'logos/cm_icon_sq.png' %>">
<meta name="msapplication-TileColor" content="#2b2d42">
<meta name="mobile-web-app-capable" content="yes">
