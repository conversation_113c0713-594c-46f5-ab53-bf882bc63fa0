<nav class="navbar navbar-expand-lg navbar-dark">
  <div class="container-fluid">
    <a class="navbar-brand" href="<%= root_path %>">
      <img src="<%= asset_path "logos/cm_logo1_glare1.svg" %>" height="50" alt="Cinematch Logo">
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav ms-auto align-items-center">
        <% if user_signed_in? && current_user %>
          <li class="nav-item">
            <span class="nav-link user-greeting">
              Hi, <%= current_user.name %>!
            </span>
          </li>
          <li class="nav-item" data-controller="recommendations-refresh">
              <button 
                id="refresh-recommendations" 
                class="nav-link d-flex align-items-center" 
                title="Refresh Recommendations" 
                data-action="click->recommendations-refresh#refresh" 
                data-recommendations-refresh-target="button"
              >
                <i class="fas fa-sync-alt fa-2x"></i><span class="sr-only">Refresh Recommendations</span>
              </button>
          </li>
          <li class="nav-item dropdown watchlist-group" data-controller="navbar-watchlist-updater">
            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="watchlistDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <div class="watchlist-content">
                <i class="fas fa-bookmark fa-lg"></i>
                <span class="font-weight-bold">Watchlist</span>
                <span class="badge rounded-pill bg-danger ms-2" id="watchlist-count" data-navbar-watchlist-updater-target="count" style="<%= current_user && current_user.unwatched_items_count > 0 ? '' : 'display: none;' %>">
                <%= current_user ? current_user.unwatched_items_count : 0 %>
                </span>
              </div>
            </a>
            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="watchlistDropdown" id="watchlist-dropdown" data-navbar-watchlist-updater-target="dropdown">
              <% if current_user && current_user.unwatched_items_count > 0 %>
                <% current_user.recent_watchlist_items.each do |item| %>
                  <% content = item.content %>
                  <% if content %>
                    <li>
                      <a class="dropdown-item d-flex align-items-center" href="/watchlist" title="<%= content.title %>">
                        <img src="<%= content.poster_url || asset_path('placeholder.png') %>" alt="" class="me-2" style="width: 30px; height: 45px; object-fit: cover; flex-shrink: 0;">
                        <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                          <%= content.title %> 
                          <small class="text-muted">(<%= content.release_year || 'N/A' %>)</small>
                        </span>
                      </a>
                    </li>
                  <% else %>
                    <li><span class="dropdown-item text-muted">Item unavailable</span></li>
                  <% end %>
                <% end %>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-primary text-center fw-bold" href="/watchlist">View All (<%= current_user.unwatched_items_count %>)</a></li>
              <% else %>
                <li><span class="dropdown-item disabled">Watchlist is empty</span></li>
              <% end %>
            </ul>
          </li>
          <% if current_user.admin? %>
            <li class="nav-item">
              <%= link_to admin_root_path, class: "nav-link", title: "Admin Dashboard" do %>
                <i class="fas fa-tachometer-alt fa-2x" aria-hidden="true"></i><span class="sr-only">Admin Dashboard</span>
              <% end %>
            </li>
          <% end %>
          <li class="nav-item">
            <%= link_to profile_path, class: "nav-link", title: "Profile" do %>
              <i class="fa-solid fa-circle-user fa-2x" aria-hidden="true"></i><span class="sr-only">Profile</span>
            <% end %>
          </li>
          <li class="nav-item">
            <%= button_to destroy_user_session_path, method: :delete, data: { turbo: false }, class: "nav-link", title: "Sign Out" do %>
              <i class="fas fa-sign-out-alt fa-2x" aria-hidden="true"></i><span class="sr-only">Sign Out</span>
            <% end %>
          </li>
        <% else %>
          <% if current_page?(root_path) %>
            <li class="nav-item">
              <%= link_to "#features", class: "nav-link", title: "Features" do %>
                Features
              <% end %>
            </li>
            <li class="nav-item">
              <%= link_to "#why", class: "nav-link", title: "Why Cinematch" do %>
                Why Cinematch
              <% end %>
            </li>
            <li class="nav-item">
              <%= link_to "#demo", class: "nav-link", title: "Demo" do %>
                Demo
              <% end %>
            </li>
            <li class="nav-item">
              <%= link_to "#contact", class: "nav-link", title: "Contact Us" do %>
                Contact Us
              <% end %>
            </li>
            <li class="nav-item">
              <%= link_to "Sign In", new_user_session_path, class: "btn btn-outline-mint btn-rounded ms-2 me-2" %>
            </li>
            <li class="nav-item">
              <%= link_to "Create Account", new_user_registration_path, class: "btn btn-jonquil btn-rounded" %>
            </li>
          <% else %>
            <li class="nav-item">
              <%= link_to new_user_session_path, class: "nav-link", title: "Sign In" do %>
                <i class="fas fa-sign-in-alt fa-lg" aria-hidden="true"></i><span class="sr-only">Sign In</span>
              <% end %>
            </li>
            <li class="nav-item">
              <%= link_to new_user_registration_path, class: "nav-link", title: "Create Account" do %>
                <i class="fas fa-user-plus fa-lg" aria-hidden="true"></i><span class="sr-only">Create Account</span>
              <% end %>
            </li>
          <% end %>
        <% end %>
      </ul>
    </div>
  </div>
</nav>
