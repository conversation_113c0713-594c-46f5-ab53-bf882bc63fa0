<div class="container-fluid" 
     data-controller="watchlist" 
     data-watchlist-sortable-resource-url-value="/watchlist/">
  <div class="row">
    <div class="col-12 mb-4">
      <h1 class="display-4 text-center">My Watchlist</h1>
    </div>
  </div>
  <div class="row">
    <div class="col-md-6">
      <div class="card mb-4 static-card">
        <div class="card-header unwatched-header d-flex justify-content-between align-items-center">
          <h2 class="h4 mb-0">Unwatched</h2>
          <span class="badge rounded-pill bg-warning text-dark" id="unwatched-count" data-watchlist-target="unwatchedCount"><%= @unwatched_items.length %></span>
        </div>
        <div class="card-body unwatched-body" id="unwatched-list" data-watchlist-target="unwatchedList">
          <%= render partial: 'watchlist_card', collection: @unwatched_items.compact, as: :item %>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card mb-4 static-card">
        <div class="card-header watched-header d-flex justify-content-between align-items-center">
          <h2 class="h4 mb-0">Watched</h2>
          <span class="badge rounded-pill bg-light text-dark" id="watched-count" data-watchlist-target="watchedCount"><%= @watched_items.length %></span>
        </div>
        <div class="card-body watched-body" id="watched-list" data-watchlist-target="watchedList">
          <%= render partial: 'watchlist_card', collection: @watched_items.compact, as: :item %>
        </div>
      </div>
    </div>
  </div>
</div>

<%= render "shared/footer" %>
<%= render 'shared/details_modal' %>
