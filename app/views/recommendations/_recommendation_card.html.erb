<div class="col-md-6 col-lg-4 mb-4">
  <div class="card h-100 show-details <%= recommendation[:in_watchlist] ? (recommendation[:rating] ? 'card-bg-watchlist-rated' : (recommendation[:watched] ? 'card-bg-watchlist-watched' : 'card-bg-watchlist-unwatched')) : '' %>" 
      id="card-<%= recommendation[:source_id] %>"
      data-id="<%= recommendation[:source_id] %>"
      data-source-id="<%= recommendation[:source_id] %>"
      data-type="<%= recommendation[:content_type] %>"
      data-in-watchlist="<%= recommendation[:in_watchlist] %>"
      data-watched="<%= recommendation[:watched] %>"
      data-rating="<%= recommendation[:rating] %>"
      role="article" 
      aria-labelledby="title-<%= recommendation[:source_id] %>">
    <div class="row g-1">
      <div class="col-4">
        <% if recommendation[:poster_url].present? %>
          <%= image_tag recommendation[:poster_url], class: "card-img", alt: "#{recommendation[:title]} poster" %>
        <% else %>
          <div class="card-img bg-secondary text-white d-flex justify-content-center align-items-center" style="height: 100%;">
            <span>No image</span>
          </div>
        <% end %>
      </div>
      <div class="col-8">
        <div class="card-body position-relative p-2">
          <h5 class="card-title d-flex justify-content-between align-items-start" id="title-<%= recommendation[:source_id] %>">
            <span class="title-text"><%= recommendation[:title] %></span>
            <% if recommendation[:match_score] %>
              <span class="badge bg-warning text-danger ms-2" role="status" aria-label="Match score"><%= recommendation[:match_score].round(2) %>%</span>
            <% end %>
          </h5>
          <div class="card-text-small text-muted mb-1" role="contentinfo" aria-label="Countries and release year">
            <%= [
              case recommendation[:production_countries]&.size
              when nil, 0
                nil
              when 1
                recommendation[:production_countries][0]['name'].gsub('United States of America', 'USA')
              when 2
                recommendation[:production_countries].map { |c| c['name'].gsub('United States of America', 'USA') }.join(' & ')
              else
                "#{recommendation[:production_countries][0]['name'].gsub('United States of America', 'USA')}, #{recommendation[:production_countries][1]['name'].gsub('United States of America', 'USA')} & #{recommendation[:production_countries].size - 2} more"
              end,
              recommendation[:release_year]
            ].compact.join(", ") %>
          </div>
          <div class="card-text-small text-muted mb-1" role="contentinfo" aria-label="TMDb rating">
            <strong>TMDb:</strong> <span aria-label="TMDb rating"><%= recommendation[:vote_average] %></span>
          </div>
          <div class="card-text-small text-muted" role="contentinfo" aria-label="Genres">
            <%= recommendation[:genres].join(", ") %>
          </div>
          <% if recommendation[:reason].present? %>
            <div class="card-text-small mt-2 fst-italic text-muted" role="contentinfo" aria-label="AI Recommendation Reason">
              <i class="fas fa-lightbulb me-1"></i>
              <%= recommendation[:reason] %>
            </div>
          <% end %>
          <div class="stretched-link show-details" data-id="<%= recommendation[:source_id] %>" data-type="<%= recommendation[:content_type] %>" role="button" aria-label="Show details for <%= recommendation[:title] %>"></div>
        </div>
        <% if recommendation[:in_watchlist] %>
          <div class="position-absolute bottom-0 end-0 m-3">
            <% if recommendation[:rating] %>
              <div class="star-score card-icon-display" title="Your rating: <%= recommendation[:rating] %>/10"><span><%= recommendation[:rating] %></span></div>
            <% elsif recommendation[:watched] %>
              <i class="fas fa-eye fs-5 card-icon-display" title="Watched"></i>
            <% else %>
              <i class="fas fa-bookmark fs-5 card-icon-display" title="In Watchlist"></i>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
