<div class="row">
  <div class="col-12 mb-4">
    <h1 class="display-4 text-center">Personalized Recommendations</h1>
  </div>
</div>

<%# Connect the Stimulus controller and define targets %>
<div id="recommendations-wrapper" data-controller="recommendations">
  <div id="recommendations-container" data-recommendations-target="container">
    <% if @recommendations.present? %>
      <%= render 'recommendations_list', recommendations: @recommendations, total_pages: @total_pages, current_page: @page %>
    <% else %>
      <%# Show loading spinner if recommendations are not immediately available %>
      <div id="loading-spinner" class="text-center" data-recommendations-target="loadingSpinner">
        <div class="spinner-border" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">We're generating your recommendations. This may take a few moments...</p>
      </div>
    <% end %>
  </div>

  <%= render "shared/footer" %>

  <%= render 'shared/details_modal' %>
        </div>
      </div>
    </div>
  </div>

</div>
