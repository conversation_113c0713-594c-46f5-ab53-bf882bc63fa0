<% if controller_name != "sessions" %>
  <%= link_to "Log in", new_session_path(resource_name) %><br />
<% end %>

<% if controller_name != "registrations" && devise_mapping.registerable? %>
  <!-- <%= link_to "Sign up", new_registration_path(resource_name) %><br /> -->
<% end %>

<% if controller_name != "passwords" && devise_mapping.recoverable? %>
  <%= link_to "Forgot your password?", new_password_path(resource_name) %><br />
<% end %>

<% if devise_mapping.confirmable? && controller_name != "confirmations" %>
  <%= link_to "Didn't receive confirmation instructions?", new_confirmation_path(resource_name) %><br />
<% end %>

<% if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != "unlocks" %>
  <%= link_to "Didn't receive unlock instructions?", new_unlock_path(resource_name) %><br />
<% end %>
