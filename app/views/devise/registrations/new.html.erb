<% content_for(:title) { "Create New Account" } %>
<% content_for(:h1) { "Create New Account" } %>
<div class="container">
  <div class="row justify-content-center align-items-center" style="min-height: calc(100vh - 200px);">
    <div class="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
      <div class="card">
        <h3 class="card-header">Create Account</h3>

        <div class="card-body">
          <%= form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>

            <div class="mb-3">
              <%= f.label :name, class: "form-label" do %>
                Name <span class="text-danger">*</span>
              <% end %>
              <%= f.text_field :name, autofocus: true, class: "form-control", required: true %>
            </div>

            <div class="mb-3">
              <%= f.label :email, class: "form-label" do %>
                Email <span class="text-danger">*</span>
              <% end %>
              <%= f.email_field :email, class: "form-control", required: true %>
            </div>

            <div class="mb-3">
              <%= f.label :gender, class: "form-label" do %>
                Gender <span class="text-muted small">(optional)</span>
              <% end %><br>
              <div class="btn-group btn-group-sm flex-wrap" role="group" aria-label="Gender">
                <%= f.radio_button :gender, "Male", id: "gender_male", class: "btn-check" %>
                <%= f.label :gender, "Male", class: "btn btn-outline-primary", for: "gender_male" %>

                <%= f.radio_button :gender, "Female", id: "gender_female", class: "btn-check" %>
                <%= f.label :gender, "Female", class: "btn btn-outline-primary", for: "gender_female" %>

                <%= f.radio_button :gender, "Non-binary", id: "gender_non_binary", class: "btn-check" %>
                <%= f.label :gender, "Non-binary", class: "btn btn-outline-primary", for: "gender_non_binary" %>

                <%= f.radio_button :gender, "Prefer not to say", id: "gender_prefer_not_to_say", class: "btn-check" %>
                <%= f.label :gender, "Prefer not to say", class: "btn btn-outline-primary", for: "gender_prefer_not_to_say" %>
              </div>
            </div>

            <div class="mb-3">
              <%= f.label :dob, class: "form-label" do %>
                Date of Birth <span class="text-danger">*</span>
              <% end %>
              <%= f.date_field :dob, 
                               start_year: 1900, 
                               end_year: Time.current.year,
                               class: "form-control",
                               required: true,
                               max: Time.current.to_date %>
              <div id="ageRequirement" class="mt-2">
                <small class="req-age text-muted">
                  <i class="bi bi-x-circle-fill"></i> You must be at least 13 years of age
                </small>
              </div>
            </div>

            <div class="mb-3">
              <%= f.label :password, class: "form-label" do %>
                Password <span class="text-danger">*</span>
              <% end %>
              <%= f.password_field :password,
                                   id: "user_password",
                                   autocomplete: "new-password", 
                                   class: "form-control",
                                   required: true,
                                   data: {
                                     complexity: {
                                       digit: 1,
                                       lower: 1,
                                       symbol: 1,
                                       upper: 1,
                                     }.to_json,
                                   } %>
              <div id="passwordStrength" class="progress mt-2" style="height: 5px;">
                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <div id="passwordRequirements" class="mt-2">
                <small class="req-length text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least 6 characters
                </small><br>
                <small class="req-upper text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one uppercase letter
                </small><br>
                <small class="req-lower text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one lowercase letter
                </small><br>
                <small class="req-digit text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one number
                </small><br>
                <small class="req-symbol text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one symbol
                </small>
              </div>
              <div id="passwordFeedback" class="invalid-feedback"></div>
            </div>

            <div class="mb-3">
              <%= f.label :password_confirmation, class: "form-label" do %>
                Password Confirmation <span class="text-danger">*</span>
              <% end %><br />
              <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "form-control", id: "password_confirmation", required: true %>
              <small id="passwordConfirmationHelp" class="form-text text-muted"></small>
            </div>

            <div class="mb-3">
              <p class="text-muted small">
                By creating account, you agree to our <%= link_to "Terms of Service", terms_path, target: "_blank" %> and <%= link_to "Privacy Policy", privacy_path, target: "_blank" %>.
              </p>
            </div>

            <div class="mb-3">
              <%= javascript_include_tag "recaptcha", "data-turbo-track": "reload" %>
              <div class="d-none d-sm-block">
                <%= recaptcha_tags size: "normal" %>
              </div>
              <div class="d-block d-sm-none">
                <%= recaptcha_tags size: "compact" %>
              </div>
            </div>

            <div class="btn-group-responsive">
              <div>
                <%= f.submit "Create Account", class: "btn btn-primary w-100" %>
              </div>
              <div class="btn-group">
                <%= link_to "Sign In", new_user_session_path, class: "btn btn-outline-primary" %>
                <%= link_to "Restore", restore_account_path, class: "btn btn-outline-secondary" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('user_password');
    const passwordConfirmation = document.getElementById('password_confirmation');
    const passwordFeedback = document.getElementById('passwordFeedback');
    const passwordConfirmationHelp = document.getElementById('passwordConfirmationHelp');
    const passwordStrength = document.getElementById('passwordStrength').querySelector('.progress-bar');
    const passwordRequirements = document.getElementById('passwordRequirements');

    function validatePasswordComplexity() {
      const complexityRequirements = JSON.parse(password.dataset.complexity);
      const errors = [];
      let strength = 0;

      const reqs = {
        length: password.value.length >= 6,
        upper: (password.value.match(/[A-Z]/g) || []).length >= complexityRequirements.upper,
        lower: (password.value.match(/[a-z]/g) || []).length >= complexityRequirements.lower,
        digit: (password.value.match(/[0-9]/g) || []).length >= complexityRequirements.digit,
        symbol: (password.value.match(/[^A-Za-z0-9]/g) || []).length >= complexityRequirements.symbol
      };

      Object.keys(reqs).forEach(req => {
        const el = passwordRequirements.querySelector(`.req-${req}`);
        const icon = el.querySelector('.bi');
        if (reqs[req]) {
          el.classList.add('text-success');
          el.classList.remove('text-muted', 'text-danger');
          icon.classList.add('bi-check-circle-fill');
          icon.classList.remove('bi-x-circle-fill');
          strength += 20;
        } else {
          el.classList.remove('text-success');
          el.classList.add('text-muted');
          icon.classList.remove('bi-check-circle-fill');
          icon.classList.add('bi-x-circle-fill');
          errors.push(el.textContent.trim());
        }
      });

      passwordStrength.style.width = `${strength}%`;
      passwordStrength.setAttribute('aria-valuenow', strength);

      if (strength < 60) {
        passwordStrength.classList.remove('bg-warning', 'bg-success');
        passwordStrength.classList.add('bg-danger');
      } else if (strength < 100) {
        passwordStrength.classList.remove('bg-danger', 'bg-success');
        passwordStrength.classList.add('bg-warning');
      } else {
        passwordStrength.classList.remove('bg-danger', 'bg-warning');
        passwordStrength.classList.add('bg-success');
      }

      if (errors.length > 0) {
        passwordFeedback.textContent = 'Please fix the highlighted issues.';
        passwordFeedback.style.display = 'block';
        password.classList.add('is-invalid');
        password.classList.remove('is-valid');
      } else {
        passwordFeedback.style.display = 'none';
        password.classList.remove('is-invalid');
        password.classList.add('is-valid');
      }
      validatePasswordMatch();
    }

    function validatePasswordMatch() {
      if (passwordConfirmation.value === '') {
        passwordConfirmationHelp.textContent = '';
        passwordConfirmation.classList.remove('is-invalid', 'is-valid');
      } else if (password.value === passwordConfirmation.value) {
        passwordConfirmationHelp.textContent = 'Passwords match';
        passwordConfirmationHelp.classList.remove('text-danger');
        passwordConfirmationHelp.classList.add('text-success');
        passwordConfirmation.classList.remove('is-invalid');
        passwordConfirmation.classList.add('is-valid');
      } else {
        passwordConfirmationHelp.textContent = 'Passwords do not match';
        passwordConfirmationHelp.classList.remove('text-success');
        passwordConfirmationHelp.classList.add('text-danger');
        passwordConfirmation.classList.add('is-invalid');
        passwordConfirmation.classList.remove('is-valid');
      }
    }

    password.addEventListener('input', function() {
      validatePasswordComplexity();
      validatePasswordMatch();
    });

    passwordConfirmation.addEventListener('input', validatePasswordMatch);
    
    // Initial validation
    validatePasswordComplexity();
    validatePasswordMatch();

    const dobField = document.querySelector('input[type="date"][name="user[dob]"]');
    if (dobField) {
      const ageRequirement = document.querySelector('.req-age');
      const ageIcon = ageRequirement.querySelector('.bi');
      
      function validateAge() {
        const selectedDate = new Date(dobField.value);
        const thirteenYearsAgo = new Date();
        const minDate = new Date('1900-01-01');
        thirteenYearsAgo.setFullYear(thirteenYearsAgo.getFullYear() - 13);
        
        if (selectedDate > thirteenYearsAgo || selectedDate < minDate) {
          const message = selectedDate < minDate ? 
            'Date of birth cannot be earlier than 1900' : 
            'You must be at least 13 years of age to register.';
          dobField.setCustomValidity(message);
          ageRequirement.classList.remove('text-success');
          ageRequirement.classList.add('text-danger');
          ageIcon.classList.remove('bi-check-circle-fill');
          ageIcon.classList.add('bi-x-circle-fill');
          dobField.classList.add('is-invalid');
          dobField.classList.remove('is-valid');
        } else {
          dobField.setCustomValidity('');
          ageRequirement.classList.add('text-success');
          ageRequirement.classList.remove('text-danger', 'text-muted');
          ageIcon.classList.add('bi-check-circle-fill');
          ageIcon.classList.remove('bi-x-circle-fill');
          dobField.classList.remove('is-invalid');
          dobField.classList.add('is-valid');
        }
      }
      
      dobField.addEventListener('change', validateAge);
      dobField.addEventListener('input', validateAge);
      
      // Initial validation
      if (dobField.value) {
        validateAge();
      }
    }
  });
</script>
