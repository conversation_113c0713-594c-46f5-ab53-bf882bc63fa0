<h2>Edit <%= resource_name.to_s.humanize %></h2>

<%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put }) do |f| %>
  <%= render "devise/shared/error_messages", resource: resource %>

  <div class="mb-3">
    <%= f.label :name, class: "form-label" %>
    <%= f.text_field :name, autofocus: true, class: "form-control" %>
  </div>

  <div class="mb-3">
    <%= f.label :email, class: "form-label" %>
    <%= f.email_field :email, class: "form-control" %>
  </div>

  <div class="mb-3">
    <%= f.label :gender, class: "form-label" %><br>
    <div class="btn-group" role="group" aria-label="Gender">
      <%= f.radio_button :gender, "Male", id: "gender_male", class: "btn-check" %>
      <%= f.label :gender, "Male", class: "btn btn-outline-primary", for: "gender_male" %>

      <%= f.radio_button :gender, "Female", id: "gender_female", class: "btn-check" %>
      <%= f.label :gender, "Female", class: "btn btn-outline-primary", for: "gender_female" %>

      <%= f.radio_button :gender, "Non-binary", id: "gender_non_binary", class: "btn-check" %>
      <%= f.label :gender, "Non-binary", class: "btn btn-outline-primary", for: "gender_non_binary" %>

      <%= f.radio_button :gender, "Prefer not to say", id: "gender_prefer_not_to_say", class: "btn-check" %>
      <%= f.label :gender, "Prefer not to say", class: "btn btn-outline-primary", for: "gender_prefer_not_to_say" %>
    </div>
  </div>

  <div class="mb-3">
    <%= f.label :dob, "Date of Birth", class: "form-label" %>
    <%= f.date_field :dob, start_year: 1900, end_year: Time.now.year, class: "form-control" %>
  </div>

  <div class="mb-3">
    <%= f.label :password, class: "form-label" %>
    <%= f.password_field :password, autocomplete: "new-password", class: "form-control", id: "password",
                                    data: {
                                      complexity: {
                                        digit: 1,
                                        lower: 1,
                                        symbol: 1,
                                        upper: 1,
                                      }.to_json,
                                    } %>
    <div id="passwordFeedback" class="invalid-feedback"></div>
    <small id="passwordHelp" class="form-text text-muted">leave blank if you don't want to change it</small>
  </div>

  <div class="mb-3">
    <%= f.label :password_confirmation, class: "form-label" %>
    <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "form-control", id: "password_confirmation" %>
    <small id="passwordConfirmationHelp" class="form-text text-muted"></small>
  </div>

  <div class="mb-3">
    <%= f.label :current_password, class: "form-label" %><i>(we need your current password to confirm your changes)</i><br />
    <%= f.password_field :current_password, autocomplete: "current-password", class: "form-control" %>
  </div>

  <div>
    <%= f.submit "Update", class: "btn btn-primary" %>
  </div>
<% end %>

<%= link_to "Back", profile_path(current_user), class: "btn btn-secondary mt-3" %>
