<% content_for :title, "Edit Your Profile" %>
<% content_for :h1, "Edit Your Profile" %>
<div class="container">
  <div class="row justify-content-center align-items-center" style="min-height: calc(100vh - 200px);">
    <div class="col-12 col-sm-10 col-md-8 col-lg-6 col-xl-5">
      <div class="card">
        <h3 class="card-header">Edit <%= resource_name.to_s.humanize %></h3>

        <div class="card-body">
          <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put, id: "edit_user" }) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>

            <div class="mb-3">
              <%= f.label :name, class: "form-label" %>
              <%= f.text_field :name, autofocus: true, class: "form-control" %>
            </div>

            <div class="mb-3">
              <%= f.label :email, class: "form-label" %>
              <div class="input-group">
                <%= f.email_field :email, class: "form-control" %>
                <% if resource.confirmed? %>
                  <span class="input-group-text text-success">
                    <i class="fas fa-check-circle" title="Email confirmed"></i>
                  </span>
                <% else %>
                  <span class="input-group-text text-warning">
                    <i class="fas fa-exclamation-circle" title="Email not confirmed"></i>
                  </span>
                  <%= link_to "Resend confirmation email", 
                              new_confirmation_path(resource_name), 
                              class: "btn btn-outline-primary input-group-text" %>
                <% end %>
              </div>
              <% unless resource.confirmed? %>
                <small class="text-muted">
                  Please check your email and click the confirmation link to verify your account.
                </small>
              <% end %>
            </div>

            <div class="mb-3">
              <%= f.label :gender, class: "form-label" %><br>
              <div class="btn-group btn-group-sm flex-wrap" role="group" aria-label="Gender">
                <%= f.radio_button :gender, "Male", id: "gender_male", class: "btn-check" %>
                <%= f.label :gender, "Male", class: "btn btn-outline-primary", for: "gender_male" %>

                <%= f.radio_button :gender, "Female", id: "gender_female", class: "btn-check" %>
                <%= f.label :gender, "Female", class: "btn btn-outline-primary", for: "gender_female" %>

                <%= f.radio_button :gender, "Non-binary", id: "gender_non_binary", class: "btn-check" %>
                <%= f.label :gender, "Non-binary", class: "btn btn-outline-primary", for: "gender_non_binary" %>

                <%= f.radio_button :gender, "Prefer not to say", id: "gender_prefer_not_to_say", class: "btn-check" %>
                <%= f.label :gender, "Prefer not to say", class: "btn btn-outline-primary", for: "gender_prefer_not_to_say" %>
              </div>
            </div>

            <div class="mb-3">
              <%= f.label :dob, "Date of Birth", class: "form-label" %>
              <%= f.date_field :dob, 
                               start_year: 1900, 
                               end_year: Time.current.year,
                               class: "form-control",
                               max: Time.current.to_date %>
              <div id="ageRequirement" class="mt-2" style="display: none;">
                <small class="req-age text-muted">
                  <i class="bi bi-x-circle-fill"></i> You must be at least 13 years of age
                </small>
              </div>
            </div>

            <div class="mb-3">
              <%= f.label :password, class: "form-label" %>
              <%= f.password_field :password, autocomplete: "new-password", class: "form-control", id: "password",
                                              data: {
                                                complexity: {
                                                  digit: 1,
                                                  lower: 1,
                                                  symbol: 1,
                                                  upper: 1,
                                                }.to_json,
                                              } %>
              <small id="passwordHelp" class="form-text text-muted">leave blank if you don't want to change it</small>
              <div id="passwordStrength" class="progress mt-2" style="height: 5px; display: none;">
                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <div id="passwordRequirements" class="mt-2" style="display: none;">
                <small class="req-length text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least 6 characters
                </small><br>
                <small class="req-upper text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one uppercase letter
                </small><br>
                <small class="req-lower text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one lowercase letter
                </small><br>
                <small class="req-digit text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one number
                </small><br>
                <small class="req-symbol text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one symbol
                </small>
              </div>
              <div id="passwordFeedback" class="invalid-feedback"></div>
            </div>

            <div class="mb-3">
              <%= f.label :password_confirmation, class: "form-label" %>
              <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "form-control", id: "password_confirmation" %>
              <small id="passwordConfirmationHelp" class="form-text text-muted"></small>
            </div>

            <div class="mb-3">
              <%= f.label :current_password, class: "form-label" %>
              <%= f.password_field :current_password, autocomplete: "current-password", class: "form-control" %>
              <small id="currentPasswordHelp" class="form-text text-muted">we need your current password to confirm your changes</small>
            </div>

            <div class="d-flex justify-content-between align-items-center">
              <div>
                <%= link_to "Back", profile_path(current_user), class: "btn btn-secondary" %>
                <%= f.submit "Update", class: "btn btn-primary" %>
          <% end %>
              </div>
              <div class="d-flex align-items-center">
                <%= button_to "Delete Account", user_path(current_user), method: :delete, 
                              class: "btn btn-danger",
                              id: "delete-account-btn" %>
              </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const deleteButton = entry.target;
        deleteButton.addEventListener('click', (event) => {
          if (!confirm("Are you sure you want to delete your account?")) {
            event.preventDefault();
          }
        });
        observer.unobserve(deleteButton);
      }
    });
  }, { threshold: 0.1 });

  const deleteButton = document.getElementById('delete-account-btn');
  if (deleteButton) {
    observer.observe(deleteButton);
  }
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const passwordConfirmation = document.getElementById('password_confirmation');
    const passwordFeedback = document.getElementById('passwordFeedback');
    const passwordConfirmationHelp = document.getElementById('passwordConfirmationHelp');
    const passwordStrength = document.getElementById('passwordStrength').querySelector('.progress-bar');
    const passwordRequirements = document.getElementById('passwordRequirements');

    function validatePasswordComplexity() {
      const complexityRequirements = JSON.parse(password.dataset.complexity);
      const errors = [];
      let strength = 0;

      const reqs = {
        length: password.value.length >= 6,
        upper: (password.value.match(/[A-Z]/g) || []).length >= complexityRequirements.upper,
        lower: (password.value.match(/[a-z]/g) || []).length >= complexityRequirements.lower,
        digit: (password.value.match(/[0-9]/g) || []).length >= complexityRequirements.digit,
        symbol: (password.value.match(/[^A-Za-z0-9]/g) || []).length >= complexityRequirements.symbol
      };

      Object.keys(reqs).forEach(req => {
        const el = passwordRequirements.querySelector(`.req-${req}`);
        const icon = el.querySelector('.bi');
        if (reqs[req]) {
          el.classList.add('text-success');
          el.classList.remove('text-muted', 'text-danger');
          icon.classList.add('bi-check-circle-fill');
          icon.classList.remove('bi-x-circle-fill');
          strength += 20;
        } else {
          el.classList.remove('text-success');
          el.classList.add('text-muted');
          icon.classList.remove('bi-check-circle-fill');
          icon.classList.add('bi-x-circle-fill');
          errors.push(el.textContent.trim());
        }
      });

      passwordStrength.style.width = `${strength}%`;
      passwordStrength.setAttribute('aria-valuenow', strength);

      if (strength < 60) {
        passwordStrength.classList.remove('bg-warning', 'bg-success');
        passwordStrength.classList.add('bg-danger');
      } else if (strength < 100) {
        passwordStrength.classList.remove('bg-danger', 'bg-success');
        passwordStrength.classList.add('bg-warning');
      } else {
        passwordStrength.classList.remove('bg-danger', 'bg-warning');
        passwordStrength.classList.add('bg-success');
      }

      if (errors.length > 0) {
        passwordFeedback.textContent = 'Please fix the highlighted issues.';
        passwordFeedback.style.display = 'block';
        password.classList.add('is-invalid');
        password.classList.remove('is-valid');
      } else {
        passwordFeedback.style.display = 'none';
        password.classList.remove('is-invalid');
        password.classList.add('is-valid');
      }
      validatePasswordMatch();
    }

    function validatePasswordMatch() {
      if (passwordConfirmation.value === '') {
        passwordConfirmationHelp.textContent = '';
        passwordConfirmation.classList.remove('is-invalid', 'is-valid');
      } else if (password.value === passwordConfirmation.value) {
        passwordConfirmationHelp.textContent = 'Passwords match';
        passwordConfirmationHelp.classList.remove('text-danger');
        passwordConfirmationHelp.classList.add('text-success');
        passwordConfirmation.classList.remove('is-invalid');
        passwordConfirmation.classList.add('is-valid');
      } else {
        passwordConfirmationHelp.textContent = 'Passwords do not match';
        passwordConfirmationHelp.classList.remove('text-success');
        passwordConfirmationHelp.classList.add('text-danger');
        passwordConfirmation.classList.add('is-invalid');
        passwordConfirmation.classList.remove('is-valid');
      }
    }

    password.addEventListener('input', function() {
      const passwordStrength = document.getElementById('passwordStrength');
      const passwordRequirements = document.getElementById('passwordRequirements');
      
      if (this.value.length > 0) {
        passwordStrength.style.display = 'block';
        passwordRequirements.style.display = 'block';
        validatePasswordComplexity();
        validatePasswordMatch();
      } else {
        passwordStrength.style.display = 'none';
        passwordRequirements.style.display = 'none';
        // Reset validation states
        this.classList.remove('is-invalid', 'is-valid');
        passwordConfirmation.classList.remove('is-invalid', 'is-valid');
        passwordConfirmationHelp.textContent = '';
      }
    });

    passwordConfirmation.addEventListener('input', validatePasswordMatch);
  });
</script>

<script>
  const dobField = document.querySelector('input[type="date"][name="user[dob]"]');
  if (dobField) {
    const ageRequirement = document.querySelector('#ageRequirement');
    const ageIcon = ageRequirement.querySelector('.bi');
    
    function validateAge() {
      const originalDate = new Date(dobField.getAttribute('data-original-value') || dobField.value);
      const selectedDate = new Date(dobField.value);
      
      // Only show validation if the date has changed
      if (selectedDate.getTime() !== originalDate.getTime()) {
        ageRequirement.style.display = 'block';
        
        const thirteenYearsAgo = new Date();
        const minDate = new Date('1900-01-01');
        thirteenYearsAgo.setFullYear(thirteenYearsAgo.getFullYear() - 13);
        
        if (selectedDate > thirteenYearsAgo || selectedDate < minDate) {
          const message = selectedDate < minDate ? 
            'Date of birth cannot be earlier than 1900' : 
            'You must be at least 13 years of age.';
          dobField.setCustomValidity(message);
          ageRequirement.querySelector('small').classList.remove('text-success');
          ageRequirement.querySelector('small').classList.add('text-danger');
          ageIcon.classList.remove('bi-check-circle-fill');
          ageIcon.classList.add('bi-x-circle-fill');
          dobField.classList.add('is-invalid');
          dobField.classList.remove('is-valid');
        } else {
          dobField.setCustomValidity('');
          ageRequirement.querySelector('small').classList.add('text-success');
          ageRequirement.querySelector('small').classList.remove('text-danger', 'text-muted');
          ageIcon.classList.add('bi-check-circle-fill');
          ageIcon.classList.remove('bi-x-circle-fill');
          dobField.classList.remove('is-invalid');
          dobField.classList.add('is-valid');
        }
      } else {
        // Hide validation if date hasn't changed
        ageRequirement.style.display = 'none';
        dobField.setCustomValidity('');
        dobField.classList.remove('is-invalid', 'is-valid');
      }
    }
    
    // Store the original value when the page loads
    dobField.setAttribute('data-original-value', dobField.value);
    
    dobField.addEventListener('change', validateAge);
    dobField.addEventListener('input', validateAge);
  }
</script>
