<% content_for :title, "Sign In to Cinematch" %>
<% content_for :h1, "Sign In to Cinematch" %>
<div class="container">
  <div class="row justify-content-center align-items-center" style="min-height: calc(100vh - 200px);">
    <div class="col-12 col-sm-8 col-md-6 col-lg-5 col-xl-4">
      <div class="card">
        <h3 class="card-header">Sign In</h3>

        <div class="card-body">
          <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>

            <div class="mb-3">
              <%= f.label :email, class: "form-label" %>
              <%= f.email_field :email, autofocus: true, class: "form-control" %>
            </div>

            <div class="mb-3">
              <%= f.label :password, class: "form-label" %>
              <%= f.password_field :password, autocomplete: "current-password", class: "form-control" %>
              <div class="small">
              <%= render "devise/shared/links" %>
              </div>
            </div>

            <% if devise_mapping.rememberable? %>
              <div class="mb-3 form-check">
                <%= f.check_box :remember_me, class: "form-check-input" %>
                <%= f.label :remember_me, class: "form-check-label" %>
              </div>
            <% end %>

            <div class="mb-3">
              <p class="text-muted small">
                By signing in, you agree to our<br><%= link_to "Terms of Service", terms_path, target: "_blank" %> and <%= link_to "Privacy Policy", privacy_path, target: "_blank" %>.
              </p>
            </div>

            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex">
                <%= f.submit "Sign In", class: "btn btn-primary" %>
              </div>
              <div class="d-flex justify-content-end align-items-center">
                <p class="mb-0 me-2">Not a user?</p>
                <%= link_to "Sign Up", new_user_registration_path, class: "btn btn-danger" %> 
              </div>
            </div>
          <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
