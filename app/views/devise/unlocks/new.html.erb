<% content_for :title, "Resend Unlock Instructions" %>
<% content_for :h1, "Resend Unlock Instructions" %>

<div class="container">
  <div class="row justify-content-center align-items-center" style="min-height: calc(100vh - 200px);">
    <div class="col-12 col-sm-8 col-md-6 col-lg-5 col-xl-3">
      <div class="card">
        <h3 class="card-header">Unlock Account</h3>

        <div class="card-body">
          <%= form_for(resource, as: resource_name, url: unlock_path(resource_name), html: { method: :post }) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>

            <div class="mb-3">
              <%= f.label :email, class: "form-label" %>
              <%= f.email_field :email, autofocus: true, class: "form-control" %>
            </div>

            <div class="mb-3">
                <%= javascript_include_tag "recaptcha", "data-turbo-track": "reload" %>
                <%= recaptcha_tags size: "compact" %>
            </div>

            <div class="actions">
              <%= f.submit "Resend unlock instructions", class: "btn btn-primary w-100" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
