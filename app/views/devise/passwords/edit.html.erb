<% content_for :title, "Change Your Password" %>
<% content_for :h1, "Change Your Password" %>
<div class="container">
  <div class="row justify-content-center align-items-center" style="min-height: calc(100vh - 200px);">
    <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3">
      <div class="card">
        <h3 class="card-header">Change Your Password</h3>

        <div class="card-body">
          <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f| %>
            <%= render "devise/shared/error_messages", resource: resource %>

            <%= f.hidden_field :reset_password_token %>

            <div class="mb-3">
              <%= f.label :password, "New password", class: "form-label" %>
              <%= f.password_field :password, autofocus: true, id: "user_password", class: "form-control", autocomplete: "new-password",
                                  required: true,
                                  data: {
                                    complexity: {
                                      digit: 1,
                                      lower: 1,
                                      symbol: 1,
                                      upper: 1,
                                    }.to_json,
                                  } %>
              <div id="passwordStrength" class="progress mt-2" style="height: 5px;">
                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
              <div id="passwordRequirements" class="mt-2">
                <small class="req-length text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least 6 characters
                </small><br>
                <small class="req-upper text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one uppercase letter
                </small><br>
                <small class="req-lower text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one lowercase letter
                </small><br>
                <small class="req-digit text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one number
                </small><br>
                <small class="req-symbol text-muted">
                  <i class="bi bi-x-circle-fill"></i> At least one symbol
                </small>
              </div>
              <div id="passwordFeedback" class="invalid-feedback"></div>
            </div>

            <div class="mb-3">
              <%= f.label :password_confirmation, "Confirm new password", class: "form-label" %>
              <%= f.password_field :password_confirmation, 
                                  id: "password_confirmation",
                                  class: "form-control", 
                                  autocomplete: "new-password", 
                                  required: true %>
              <small id="passwordConfirmationHelp" class="form-text"></small>
            </div>

            <div class="d-flex justify-content-between">
              <%= link_to "Log in", new_user_session_path, class: "btn btn-secondary" %>
              <%= f.submit "Change my password", class: "btn btn-primary" %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('user_password');
    const passwordConfirmation = document.getElementById('password_confirmation');
    const passwordFeedback = document.getElementById('passwordFeedback');
    const passwordConfirmationHelp = document.getElementById('passwordConfirmationHelp');
    const passwordStrength = document.getElementById('passwordStrength').querySelector('.progress-bar');
    const passwordRequirements = document.getElementById('passwordRequirements');

    function validatePasswordComplexity() {
      const complexityRequirements = JSON.parse(password.dataset.complexity);
      const errors = [];
      let strength = 0;

      const reqs = {
        length: password.value.length >= 6,
        upper: (password.value.match(/[A-Z]/g) || []).length >= complexityRequirements.upper,
        lower: (password.value.match(/[a-z]/g) || []).length >= complexityRequirements.lower,
        digit: (password.value.match(/[0-9]/g) || []).length >= complexityRequirements.digit,
        symbol: (password.value.match(/[^A-Za-z0-9]/g) || []).length >= complexityRequirements.symbol
      };

      Object.keys(reqs).forEach(req => {
        const el = passwordRequirements.querySelector(`.req-${req}`);
        const icon = el.querySelector('.bi');
        if (reqs[req]) {
          el.classList.add('text-success');
          el.classList.remove('text-muted', 'text-danger');
          icon.classList.add('bi-check-circle-fill');
          icon.classList.remove('bi-x-circle-fill');
          strength += 20;
        } else {
          el.classList.remove('text-success');
          el.classList.add('text-muted');
          icon.classList.remove('bi-check-circle-fill');
          icon.classList.add('bi-x-circle-fill');
          errors.push(el.textContent.trim());
        }
      });

      passwordStrength.style.width = `${strength}%`;
      passwordStrength.setAttribute('aria-valuenow', strength);

      if (strength < 60) {
        passwordStrength.classList.remove('bg-warning', 'bg-success');
        passwordStrength.classList.add('bg-danger');
      } else if (strength < 100) {
        passwordStrength.classList.remove('bg-danger', 'bg-success');
        passwordStrength.classList.add('bg-warning');
      } else {
        passwordStrength.classList.remove('bg-danger', 'bg-warning');
        passwordStrength.classList.add('bg-success');
      }

      if (errors.length > 0) {
        passwordFeedback.textContent = 'Please fix the highlighted issues.';
        passwordFeedback.style.display = 'block';
        password.classList.add('is-invalid');
        password.classList.remove('is-valid');
      } else {
        passwordFeedback.style.display = 'none';
        password.classList.remove('is-invalid');
        password.classList.add('is-valid');
      }
      validatePasswordMatch();
    }

    function validatePasswordMatch() {
      if (passwordConfirmation.value === '') {
        passwordConfirmationHelp.textContent = '';
        passwordConfirmation.classList.remove('is-invalid', 'is-valid');
      } else if (password.value === passwordConfirmation.value) {
        passwordConfirmationHelp.textContent = 'Passwords match';
        passwordConfirmationHelp.classList.remove('text-danger');
        passwordConfirmationHelp.classList.add('text-success');
        passwordConfirmation.classList.remove('is-invalid');
        passwordConfirmation.classList.add('is-valid');
      } else {
        passwordConfirmationHelp.textContent = 'Passwords do not match';
        passwordConfirmationHelp.classList.remove('text-success');
        passwordConfirmationHelp.classList.add('text-danger');
        passwordConfirmation.classList.add('is-invalid');
        passwordConfirmation.classList.remove('is-valid');
      }
    }

    password.addEventListener('input', function() {
      validatePasswordComplexity();
      validatePasswordMatch();
    });

    passwordConfirmation.addEventListener('input', validatePasswordMatch);
    
    // Initial validation
    validatePasswordComplexity();
    validatePasswordMatch();
  });
</script>
