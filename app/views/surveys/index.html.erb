<% content_for :title, "Personalization Survey" %>
<% content_for :head do %>
  <meta name="survey-type" content="<%= @survey_type %>">
  <meta name="retake" content="<%= @retake %>">
<% end %>

<div class="container-fluid p-0">
  <div class="d-flex justify-content-center align-items-center" style="min-height: calc(100vh - 200px);">
    <%= render 'welcome_modal' if @show_welcome_modal %>
    
    <div class="card shadow survey-card">
      <div class="card-header">
        <h3 class="mb-0">
          <%= @survey_type.to_s == 'basic' ? 'Personalization Survey' : 'Extended Personalization Survey' %>
        </h3>
      </div>

      <div class="card-body p-4 survey-card-body">
        <div class="progress survey-progress mb-4">
          <div class="progress-bar survey-progress-bar" 
               role="progressbar" 
               data-progress
               style="width: <%= @progress %>%;"
               aria-valuenow="<%= @progress %>" 
               aria-valuemin="0" 
               aria-valuemax="100">
          </div>
        </div>

        <%= form_with url: surveys_path(type: @survey_type), 
                      local: false, 
                      html: { 
                        id: "survey-form",
                        class: "survey-form",
                        data: { survey_type: @survey_type }
                      } do |form| %>
          
          <div class="survey-container">
            <%= render 'question_section', form: form, questions: @questions %>
            <%= render 'genre_selection', form: form, genres: @genres if @survey_type == 'basic' %>
          </div>
          <%= render 'survey_navigation', survey_type: @survey_type, retake: @retake %>
        <% end %>
      </div>
    </div>
  </div>
</div>

<%= render 'survey_completion_modal' %>
<%= render 'save_progress_modal' if @survey_type.to_s == 'extended' %>
