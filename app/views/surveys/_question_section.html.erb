<div class="question-container" data-total-questions="<%= @total_questions %>">
  <% if questions&.any? %>
    <% questions.each_with_index do |question, index| %>
      <div class="question-card" 
           data-question-id="<%= question.id %>"
           data-question-index="<%= index %>"
           data-saved-response="<%= @saved_responses[question.id] if @saved_responses %>"
           <% if question.attention_check? %>
           data-attention-check="true"
           data-correct-answer="<%= question.correct_answer %>"
           <% end %>
           style="display: <%= index == 0 ? 'block' : 'none' %>;">
        
        <div class="text-end mb-2">
          <small class="text-muted">Question <%= index + 1 %> of <%= @total_questions %></small>
        </div>
        
        <div class="text-center mb-4">
          <h4 class="question-text">
            <%= question.question_text %>
          </h4>

          <div class="response-options d-flex justify-content-between align-items-stretch gap-2">
            <% %w[Strongly_Disagree Disagree Neutral Agree Strongly_Agree].each do |response| %>
              <button type="button"
                      class="response-button flex-1"
                      data-value="<%= response %>">
                <%= response.humanize %>
              </button>
            <% end %>
          </div>
        </div>

        <% if question.attention_check? %>
          <div class="alert alert-info mt-3 attention-check-notice" style="display: none;">
            <i class="fas fa-exclamation-circle"></i> Please read this question carefully.
          </div>
        <% end %>
      </div>
    <% end %>
  <% else %>
    <div class="alert alert-warning">
      No questions available. Please contact the administrator.
    </div>
  <% end %>
</div> 
