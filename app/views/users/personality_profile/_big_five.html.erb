<!-- Big Five Tab -->
<div class="tab-pane fade" id="big5" role="tabpanel" aria-labelledby="big5-tab">
  <% if personality_profile[:big_five].present? %>
    <div class="mb-5">
      <h3 class="mb-3">Big Five Personality Traits</h3>
      <div class="alert alert-light border mb-4">
        <p class="mb-0" style="font-size: 0.9rem;">The Big Five personality traits represent the most widely accepted model of personality in psychology. Your scores indicate your relative position on each dimension compared to the general population.</p>
      </div>

      <!-- Chart Section -->
      <div class="text-center mb-4">
        <div style="height: 300px; max-width: 600px; margin: 0 auto;">
          <canvas id="bigFiveChart"></canvas>
        </div>
        <p class="text-muted mt-2" style="font-size: 0.8rem;">The further a point is from the center, the stronger that trait is in your personality.</p>
      </div>

      <!-- Traits Section -->
      <div class="row">
        <% trait_descriptions = {
          openness: "Openness reflects curiosity, creativity, and preference for variety. High scorers tend to be imaginative and adventurous.",
          conscientiousness: "Conscientiousness indicates organization, dependability, and self-discipline. High scorers are typically careful and diligent.",
          extraversion: "Extraversion relates to sociability, assertiveness, and emotional expressiveness. High scorers tend to be outgoing and energetic.",
          agreeableness: "Agreeableness represents traits like trust, altruism, and cooperation. High scorers are typically friendly and compassionate.",
          neuroticism: "Neuroticism reflects emotional stability and anxiety levels. High scorers may experience more stress and emotional reactivity."
        } %>
        <% trait_implications = {
          openness: {
            high: "You likely enjoy films with complex narratives and artistic elements",
            medium: "You appreciate both familiar genres and novel experiences",
            low: "You may prefer straightforward storytelling and familiar genres"
          },
          conscientiousness: {
            high: "You might enjoy well-crafted plots with logical progression",
            medium: "You can appreciate both structured and free-flowing narratives",
            low: "You may enjoy spontaneous and unpredictable stories"
          },
          extraversion: {
            high: "You likely enjoy high-energy films with social themes",
            medium: "You appreciate both social and introspective stories",
            low: "You might prefer thoughtful, introspective films"
          },
          agreeableness: {
            high: "You may be drawn to stories about cooperation and connection",
            medium: "You can appreciate both harmonious and conflict-driven narratives",
            low: "You might enjoy stories with strategic thinking and complexity"
          },
          neuroticism: {
            high: "You may connect with emotionally complex narratives",
            medium: "You can engage with both intense and serene storytelling",
            low: "You might prefer stories with emotional balance"
          }
        } %>
        <% personality_profile[:big_five].except(:personality_type).each_slice(3).each_with_index do |traits_group, index| %>
          <div class="col-md-6">
            <% traits_group.each do |trait, score| %>
              <div class="mb-3">
                <div class="trait-score">
                  <div class="d-flex justify-content-between mb-1">
                    <label>
                      <span data-bs-toggle="tooltip" data-bs-placement="top" title="<%= trait_descriptions[trait.to_sym] %>">
                        <strong><%= trait.to_s.titleize %></strong>
                      </span>
                    </label>
                    <span><%= score %>%</span>
                  </div>
                  <div class="progress mb-2">
                    <div class="progress-bar bg-primary" 
                         role="progressbar" 
                         style="width: <%= score %>%"
                         aria-valuenow="<%= score %>" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                  </div>
                  <% 
                    level = if score >= 70
                      "high"
                    elsif score <= 30
                      "low"
                    else
                      "medium"
                    end
                  %>
                  <small class="text-muted"><%= trait_implications[trait.to_sym][level.to_sym] %></small>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>
</div> 
