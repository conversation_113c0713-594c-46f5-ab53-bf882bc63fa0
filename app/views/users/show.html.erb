<% content_for :title, "Your Profile Dashboard" %>
<% content_for :h1, "Your Profile Dashboard" %>
<div class="container-fluid d-flex justify-content-center align-items-center" style="min-height: calc(100vh - 200px);">
  <div class="card w-100" style="max-width: 800px;">
    <div class="card-header">
      <h2 class="mb-0">Your Profile</h2>
    </div>
    <div class="card-body p-0">
      <div class="accordion w-100" id="profileAccordion">

        <div class="accordion-item">
          <h2 class="accordion-header" id="personalityProfileHeading">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#personalityProfileCollapse" aria-expanded="true" aria-controls="personalityProfileCollapse">
              Personality Profile
            </button>
          </h2>
          <div id="personalityProfileCollapse" class="accordion-collapse collapse show" aria-labelledby="personalityProfileHeading" data-bs-parent="#profileAccordion">
            <div class="accordion-body">
              <%= render "users/personality_profile" %>
            </div>
          </div>
        </div>

        <div class="accordion-item">
          <h2 class="accordion-header" id="contentPreferencesHeading">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#contentPreferencesCollapse" aria-expanded="false" aria-controls="contentPreferencesCollapse">
              Content Preferences
            </button>
          </h2>
          <div id="contentPreferencesCollapse" class="accordion-collapse collapse" aria-labelledby="contentPreferencesHeading" data-bs-parent="#profileAccordion">
            <div class="accordion-body">
              <%= render "users/content_preferences" %>
            </div>
          </div>
        </div>
        <div class="accordion-item">
          <h2 class="accordion-header" id="settingsHeading">
            <button class="accordion-button collapsed rounded-0" type="button" data-bs-toggle="collapse" data-bs-target="#settingsCollapse" aria-expanded="false" aria-controls="settingsCollapse">
              User Settings
            </button>
          </h2>
          <div id="settingsCollapse" class="accordion-collapse collapse" aria-labelledby="settingsHeading" data-bs-parent="#profileAccordion">
            <div class="accordion-body">
              <h3 class="mt-4">Edit Personal Information</h3>
              <%= render "users/edit" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%= render "users/profile_charts_js" %>

<style>
  /* Custom styles for vertical tabs using our color palette */
  .nav-pills .nav-link {
    color: var(--bs-dark);
    background-color: var(--bs-light);
    border: 1px solid var(--bs-gray-300);
    margin-bottom: 0.5rem;
    padding: 0.75rem 1rem;
    transition: background-color 0.2s ease-in-out;
    font-weight: 500;
  }

  .nav-pills .nav-link:hover {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: var(--bs-white);
  }

  .nav-pills .nav-link.active {
    color: var(--bs-white);
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    box-shadow: 0 2px 4px rgba(var(--bs-primary-rgb), 0.25);
  }

  /* Simplified tab content area */
  .tab-content {
    background: transparent;
    border: none;
    padding: 0;
    box-shadow: none;
  }

  /* Improve visibility of icons in tabs */
  .nav-pills .nav-link i {
    opacity: 0.7;
  }

  .nav-pills .nav-link:hover i,
  .nav-pills .nav-link.active i {
    opacity: 1;
  }

  /* Remove transform effect */
  .nav-pills .nav-link:hover,
  .nav-pills .nav-link.active {
    transform: none;
  }
</style>
