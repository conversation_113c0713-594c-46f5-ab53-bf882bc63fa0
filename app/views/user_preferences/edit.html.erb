<div class="d-flex justify-content-center">
  <div class="card w-75">
    <h2 class="card-header">Edit Preferences</h1>

    <div class="card-body">
      <%= form_with model: @user_preference, local: true do |form| %>
        <div class="mb-3">
          <%= form.label :favorite_genres, "Select your favorite genres", class: "form-label" %><br>
          <div class="btn-group-toggle d-flex flex-wrap" data-toggle="buttons">
            <% @genres.each do |genre| %>
              <%= form.check_box :favorite_genres, { multiple: true, class: "btn-check", id: genre["name"].parameterize }, genre["name"], nil %>
              <%= label_tag genre["name"].parameterize, genre["name"], class: "btn btn-outline-primary m-1" %>
            <% end %>
          </div>
        </div>
        <div class="ai-preferences mb-4">
          <div class="form-check form-switch">
            <%= form.check_box :use_ai, class: "form-check-input", role: "switch", data: { action: "change->preferences#toggleAiOptions" } %>
            <%= form.label :use_ai, "Use AI for recommendations", class: "form-check-label" %>
          </div>

          <div id="ai-options" class="mt-3 ms-4" data-preferences-target="aiOptions">
            <div class="form-group">
              <%= form.label :ai_model, "AI Model", class: "form-label" %>
              <%= form.select :ai_model, 
                  @available_models.map { |key, config| 
                    [
                      "#{config[:name]} - #{config[:description]}", 
                      key, 
                      { 
                        data: { 
                          cost: config[:cost_per_1k],
                          description: config[:description]
                        } 
                      }
                    ]
                  },
                  { include_blank: false },
                  class: "form-select",
                  data: { 
                    action: "change->preferences#updateModelInfo",
                    preferences-target: "modelSelect"
                  }
              %>
              <small class="form-text text-muted mt-1" data-preferences-target="modelInfo">
                <%= @available_models[@user_preference.ai_model][:description] %>
              </small>
            </div>
          </div>
        </div>
        <div>
          <%= form.submit "Save Preferences", class: "btn btn-primary" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
