class AiModelsConfig
  MODELS = {
    'gemini-2-5-pro' => {
      provider: :gemini,
      api_name: 'gemini-2.5-pro-exp-03-25',
      name: 'Gemini 2.5 Pro',
      context_window: 2097152,
      max_tokens: 8192,
      temperature: 0.7,
      input_cost_per_1M: 1.25,
      output_cost_per_1M: 10.0,
      description: 'Latest Gemini model with advanced reasoning capabilities'
    },
    'gemini-2-flash' => {
      provider: :gemini,
      api_name: 'gemini-2.0-flash',
      name: 'Gemini 2.0 Flash',
      context_window: 1048576,
      max_tokens: 8192,
      temperature: 0.7,
      input_cost_per_1M: 0.1,
      output_cost_per_1M: 0.4,
      description: 'Fast and efficient, optimized for speed'
    },
    'gemini-2-flash-lite' => {
      provider: :gemini,
      api_name: 'gemini-2.0-flash-lite',
      name: 'Gemini 2.0 Flash-Lite',
      context_window: 1048576,
      max_tokens: 8192,
      temperature: 0.7,
      input_cost_per_1M: 0.075,
      output_cost_per_1M: 0.30,
      description: 'Smallest and most cost-effective Gemini model for at-scale usage'
    },
    'gemini-2-pro-exp' => {
      provider: :gemini,
      api_name: 'gemini-2.0-pro-exp-02-05',
      name: 'Gemini 2.0 Pro (Experimental)',
      context_window: 2097152,
      max_tokens: 8192,
      temperature: 0.7,
      input_cost_per_1M: 0,
      output_cost_per_1M: 0,
      description: 'Enhanced capabilities for nuanced suggestions'
    },
    'gemma-3' => {
      provider: :gemini,
      api_name: 'gemma-3-27b-it',
      name: 'Gemma 3',
      context_window: 1048576,
      max_tokens: 8192,
      temperature: 0.7,
      input_cost_per_1M: 0.1,
      output_cost_per_1M: 0.4,
      description: 'Lightweight, state-of-the-art open model from Google'
    },
    'gpt-4o-mini' => {
      provider: :openai,
      api_name: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      context_window: 128000,
      max_tokens: 16384,
      temperature: 0.7,
      input_cost_per_1M: 0.15,
      output_cost_per_1M: 0.6,
      description: 'Cost-effective with high-quality output'
    },
    'claude-3-5-haiku' => {
      provider: :anthropic,
      api_name: 'claude-3-5-haiku-latest',
      name: 'Claude 3.5 Haiku',
      context_window: 200000,
      max_tokens: 8192,
      temperature: 0.7,
      input_cost_per_1M: 0.8,
      output_cost_per_1M: 4.0,
      description: 'Rapid recommendations with high accuracy'
    },
    'claude-3-haiku' => {
      provider: :anthropic,
      api_name: 'claude-3-haiku-20240307',
      name: 'Claude 3 Haiku',
      context_window: 200000,
      max_tokens: 4096,
      temperature: 0.7,
      input_cost_per_1M: 0.25,
      output_cost_per_1M: 1.25,
      description: 'Quick performance for instant results'
    },
    'llama-4-maverick' => {
      provider: :together,
      api_name: 'meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8',
      name: 'Llama 4 Maverick',
      context_window: 128000,
      max_tokens: 4096,
      temperature: 0.7,
      input_cost_per_1M: 0.27,
      output_cost_per_1M: 0.85,
      description: 'Advanced Llama 4 model with extended context'
    },
    'llama-4-scout' => {
      provider: :together,
      api_name: 'meta-llama/Llama-4-Scout-17B-16E-Instruct',
      name: 'Llama 4 Scout',
      context_window: 128000,
      max_tokens: 4096,
      temperature: 0.7,
      input_cost_per_1M: 0.18,
      output_cost_per_1M: 0.59,
      description: 'Efficient Llama 4 model for quick responses'
    },
    'llama-3-turbo' => {
      provider: :together,
      api_name: 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free',
      name: 'Llama 3 Turbo',
      context_window: 128000,
      max_tokens: 2048,
      temperature: 0.7,
      input_cost_per_1M: 0,
      output_cost_per_1M: 0,
      description: 'Free high-performance recommendations'
    },
    'deepseek-chat' => {
      provider: :together,
      api_name: 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free',
      name: 'DeepSeek R1',
      context_window: 128000,
      max_tokens: 8192,
      temperature: 0.7,
      input_cost_per_1M: 0,
      output_cost_per_1M: 0,
      description: 'Strong recommendation capabilities'
    },
    'ollama-llama3' => {
      provider: :ollama,
      api_name: 'llama3:latest',
      name: 'Llama 3 (Local)',
      context_window: 8192,
      max_tokens: 4096,
      temperature: 0.7,
      input_cost_per_1M: 0,
      output_cost_per_1M: 0,
      description: 'Local Llama 3 model for development (no API costs)',
      local_only: true
    },
    'ollama-llama3-2' => {
      provider: :ollama,
      api_name: 'llama3.2:latest',
      name: 'Llama 3.2 (Local)',
      context_window: 8192,
      max_tokens: 4096,
      temperature: 0.7,
      input_cost_per_1M: 0,
      output_cost_per_1M: 0,
      description: 'Local Llama 3.2 model for development (no API costs)',
      local_only: true
    },
    'ollama-embed-large' => {
      provider: :ollama,
      api_name: 'mxbai-embed-large:latest',
      name: 'Embed Large (Local)',
      context_window: 8192,
      max_tokens: 4096,
      temperature: 0.7,
      input_cost_per_1M: 0,
      output_cost_per_1M: 0,
      description: 'Local embedding model for development (no API costs)',
      local_only: true,
      embedding_model: true
    }
  }.freeze

  def self.default_model
    'gemini-2-flash'
  end

  def self.available_models
    if Rails.env.development?
      MODELS
    else
      MODELS.reject { |_, config| config[:local_only] == true }
    end
  end
  
  def self.local_models
    MODELS.select { |_, config| config[:local_only] == true }
  end
  
  def self.remote_models
    MODELS.reject { |_, config| config[:local_only] == true }
  end
end 
