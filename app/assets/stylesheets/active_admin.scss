// Sass variable overrides must be declared before loading up Active Admin's styles.
//
// To view the variables that Active Admin provides, take a look at
// `app/assets/stylesheets/active_admin/mixins/_variables.scss` in the
// Active Admin source.
//
// For example, to change the sidebar width:
// $sidebar-width: 242px;

// Active Admin's got SASS!
@import "active_admin/mixins";
@import "active_admin/base";
@import "admin/good_job_dashboard";

// Overriding any non-variable Sass must be done after the fact.
// For example, to change the default status-tag color:
//
//   .status_tag { background: #6090DB; }

.good-job-dashboard {
  // Styles are now in admin/good_job_dashboard.scss
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-box {
  // Some styles are now in admin/good_job_dashboard.scss
  // Keep only the ones specific to the dashboard stats
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  text-align: center;

  h3 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  h2 {
    color: #2E2F30;
    font-size: 1.5rem;
    margin: 0;
  }
}

.recent-content {
  margin-top: 2rem;
}

.job-stats {
  margin-bottom: 2rem;
}

.actions {
  // Some styles are now in admin/good_job_dashboard.scss
  // Keep only the ones specific to the dashboard actions
  margin-top: 1rem;
  text-align: right;
}

.trend-observations {
  background: #fff;
  padding: 1rem;
  margin-top: 1rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);

  h3 {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }

  p {
    margin-bottom: 0.5rem;
    color: #2E2F30;
  }
}

.tabs {
  margin-top: 1rem;
  
  .tab-content {
    padding: 1rem;
    background: #fff;
    border: 1px solid #ddd;
    border-top: none;
  }
}

.underage {
  background-color: rgba(255, 0, 0, 0.1) !important;
}

.status_tag {
  &.error {
    background-color: #d45f53;
  }
  &.warning {
    background-color: #e29b20;
  }
  &.ok {
    background-color: #8daa92;
  }
}
