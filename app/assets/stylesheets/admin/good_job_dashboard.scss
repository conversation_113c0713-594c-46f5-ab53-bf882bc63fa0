// Good Job Dashboard Styles
// Consolidated from multiple sources

// Main dashboard container
.good-job-dashboard {
  font-family: Arial, sans-serif;
  
  h3 {
    color: #333;
    margin-top: 20px;
  }
  
  ul {
    list-style-type: none;
    padding: 0;
  }
}

// Statistics grid and boxes
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-box {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  
  h4 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 1.2rem;
  }
  
  p {
    margin-bottom: 0;
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
  }
}

// Filter controls
.filter-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.job-filter,
.status-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f8f9fa;
  min-width: 200px;
}

// Refresh controls
.refresh-controls {
  display: flex;
  align-items: center;
  margin: 15px 0;
  flex-wrap: wrap;
  gap: 10px;
}

.refresh-button {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #357ab8;
  }
  
  &.loading {
    background-color: #999;
    cursor: wait;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
      width: 12px;
      height: 12px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top-color: white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  to { transform: translateY(-50%) rotate(360deg); }
}

.auto-refresh-indicator {
  color: #666;
  font-size: 13px;
  white-space: nowrap;
  margin-left: 10px;
}

// Jobs table
.jobs-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  
  th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }
  
  th {
    background-color: #f2f2f2;
    font-weight: bold;
  }
  
  tr:hover {
    background-color: #f5f5f5;
  }
  
  tr.error {
    background-color: #fff3f3;
  }
}

// Delete job button
.delete-job {
  color: #dc3545;
  cursor: pointer;
  text-decoration: none;
}

.delete-job:hover {
  text-decoration: underline;
}

// Status box
.status-box {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 1.5rem;
}

.status-ok {
  border-left: 5px solid #4caf50;
}

.status-warning {
  border-left: 5px solid #ff9800;
}

.status-error {
  border-left: 5px solid #f44336;
}

.status-checking {
  border-left: 5px solid #2196f3;
}

// Job runner status container
#job-runner-status-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

// Error message
.error-message {
  color: #dc3545;
  font-weight: bold;
  margin-top: 10px;
}

// Local job status
.local-job-status {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ddd;
}

// Job form
.job-form {
  margin-top: 10px;
  
  .form-group {
    margin-bottom: 10px;
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    .form-control {
      width: 100%;
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
  }
}

// Job buttons
.job-button-container {
  background: #f9f9f9;
  padding: 1.5rem;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.job-button-container h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #333;
  font-size: 1.2rem;
}

.job-button-container p {
  margin-bottom: 1rem;
  color: #666;
  font-size: 0.9rem;
}

.job-button {
  display: inline-block;
  padding: 0.7rem 1.4rem;
  background-color: #2271b1;
  color: white !important;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.2s;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.job-button:hover {
  background-color: #135e96;
  color: white !important;
  box-shadow: 0 3px 6px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.job-sub-buttons {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.job-sub-button {
  display: inline-block;
  padding: 0.5rem 0.9rem;
  background-color: #f8f8f8;
  color: #0366d6 !important;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid #ddd;
  transition: all 0.2s;
}

.job-sub-button:hover {
  background-color: #f0f0f0;
  color: #0256b9 !important;
  border-color: #bbb;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

// Error details
.toggle-error-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 3px 8px;
  font-size: 0.8rem;
  cursor: pointer;
}

.toggle-error-btn:hover {
  background-color: #e0e0e0;
}

.error-details {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff3f3;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 0.9rem;
  }
}

.hidden {
  display: none;
}

// Status indicators
.status-indicator {
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 5px;
}

.status-indicator.available {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-indicator.unavailable {
  background-color: #ffebee;
  color: #c62828;
}

.status-link {
  display: block;
  font-size: 0.9rem;
  color: #0366d6;
  text-decoration: underline;
}

// Manual jobs panel
.manual-jobs-panel {
  margin-bottom: 2rem;
}

.job-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

// Job runner status panel
.job-runner-status-panel {
  margin: 20px 0;
}

// Back link
.back-link {
  margin-top: 2rem;
}

// Actions
.actions {
  margin-top: 1.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

// Delete job button
.delete-job-btn {
  display: inline-block;
  padding: 3px 8px;
  background-color: #f8d7da;
  color: #721c24 !important;
  border-radius: 3px;
  text-decoration: none;
  font-size: 0.8rem;
  border: 1px solid #f5c6cb;
  transition: all 0.2s;
}

.delete-job-btn:hover {
  background-color: #f1b0b7;
  color: #721c24 !important;
  border-color: #f1b0b7;
} 
