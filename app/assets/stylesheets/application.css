:root {
  --space-cadet: #2b2d42ff;
  --jonquil: #efc81aff;
  --mint-green: #e9fff9ff;
  --engineering-orange: #ba2d0bff;
  --tufts-blue: #1a8fe3ff;
  --golden-glare: #ffd700ff;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", <PERSON>l, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

body {
  font-family: var(--bs-body-font-family);
  color: var(--space-cadet);
  background: 
    radial-gradient(
      circle at center,
      rgba(233, 255, 249, 0.7) 0%, /* mint-green */
      rgba(239, 200, 26, 0.5) 25%, /* jonquil */
      rgba(26, 143, 227, 0.5) 50%, /* tufts-blue */
      rgba(186, 45, 11, 0.5) 75%, /* engineering-orange */
      rgba(43, 45, 66, 0.7) 100% /* space-cadet */
    ),
    var(--mint-green); /* solid background color */
  background-size: 400% 400%;
  background-attachment: fixed;
  animation: gradientAnimation 60s ease infinite;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--space-cadet);
  font-weight: 700;
}

.navbar {
  background-color: var(--space-cadet);
  padding: 10px 20px;
}

.navbar-brand {
  color: var(--jonquil);
}

.nav-link {
  color: var(--mint-green);
}

.nav-link:hover {
  color: var(--jonquil);
}

.user-greeting {
  font-size: 1.2rem; 
  color: var(--jonquil); 
  margin-right: 10px;
}

.btn-primary {
  background-color: var(--tufts-blue);
  border-color: var(--tufts-blue);
  color: var(--mint-green);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--jonquil);
  border-color: var(--jonquil);
  color: var(--space-cadet);
}

.btn-danger {
  background-color: var(--engineering-orange);
  border-color: var(--engineering-orange);
  color: var(--jonquil);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Custom styles for radio buttons as toggle buttons */
.btn-check:checked+.btn-outline-primary,
.btn-check:active+.btn-outline-primary {
  color: var(--mint-green);
  background-color: var(--tufts-blue);
  border-color: var(--tufts-blue);
}

.btn-check:focus+.btn-outline-primary,
.btn-check:checked:focus+.btn-outline-primary {
  box-shadow: 0 0 0 0.25rem rgba(26, 143, 227, 0.5);
}

.btn-check:hover+.btn-outline-primary {
  color: var(--engineering-orange);
  background-color: var(--mint-green);
  border-color: var(--engineering-orange);
}

.btn-outline-primary {
  color: var(--tufts-blue);
  border-color: var(--tufts-blue);
}

.btn-outline-primary:hover {
  background-color: var(--jonquil);
  border-color: var(--jonquil);
  color: var(--space-cadet);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
  color: var(--engineering-orange);
  background-color: var(--mint-green);
  border-color: var(--engineering-orange);
}

.btn-outline-primary:checked,
.btn-outline-primary:active,
.btn-outline-primary.active {
  color: var(--mint-green);
  background-color: var(--tufts-blue);
  border-color: var(--tufts-blue);
}

.btn-group-toggle {
  display: flex;
  flex-wrap: wrap;
}

.btn-group-toggle .btn {
  margin: 0.2rem;
}

.btn-close {
  background: transparent;
  border: none;
  color: var(--mint-green);
}

.btn-close:hover {
  color: var(--jonquil);
}

.form-control {
  border-radius: 0.25rem;
  border: 1px solid var(--tufts-blue);
}

.form-control:focus {
  border-color: var(--engineering-orange);
  box-shadow: none;
}

.container {
  padding-top: 20px;
}

.card {
  max-width: 540px;
  height: auto;
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.card-header {
  background-color: var(--jonquil);
  color: var(--engineering-orange);
}

.card-img {
  height: 225px; /* Adjust this value as needed */
  object-fit: cover;
  width: 100%;
}

.card:hover {
  transform: scale(1.02);
  
}

.card-body {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.card-text-small {
  font-size: 0.8rem;
  margin-bottom: 0.25rem;
}

.badge {
  background-color: var(--jonquil);
  color: var(--engineering-orange);
  font-size: 1rem;
  padding: 0.5em 1em;
  border-radius: 0.25rem;
}

.badge-custom {
  background-color: var(--jonquil);
  color: var(--engineering-orange);
  font-weight: bold;
  padding: 0.5em 1em;
  border-radius: 0.25rem;
}

.badge-large {
  font-size: 1.5rem;
  padding: 0.5em 0.7em;
  background-color: var(--jonquil);
  color: var(--engineering-orange);
  border-radius: 0.5rem;
}

.modal-body {
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: var(--space-cadet);
  color: var(--mint-green);
}

.modal-body h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.modal-body .badge-large {
  margin-left: 1rem;
}

.modal-content {
  background-color: var(--space-cadet);
  color: var(--mint-green);
}

.modal-title {
  color: var(--jonquil);
}

.modal-header {
  border-bottom: 1px solid var(--engineering-orange);
  background-color: var(--engineering-orange);
  color: var(--mint-green);
}

.modal-header .btn-close {
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23e9fff9'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  color: var(--mint-green);
  opacity: 0.8;
}

.modal-header .btn-close:hover {
  opacity: 1;
  color: var(--jonquil);
}

.modal-header .btn-close:focus {
  box-shadow: 0 0 0 0.25rem rgba(233, 255, 249, 0.25);
}

.modal-body p {
  margin-bottom: 0.5rem;
}

/* Adjustments for better spacing */
.p-1 {
  padding: 0.25rem !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.profile-container {
  display: flex;
  justify-content: center;
  width: 100%;
}

#profileAccordion {
  width: 100%;
  max-width: 800px;
}

.accordion-item {
  background-color: var(--space-cadet);
}

.accordion-header .accordion-button {
  background-color: var(--engineering-orange);
  color: var(--mint-green);
  font-weight: 700;
}

.accordion-header .accordion-button:focus {
  box-shadow: none;
}

.accordion-header .accordion-button:not(.collapsed) {
  background-color: var(--tufts-blue);
  color: var(--mint-green);
}

.accordion-header .accordion-button:hover {
  background-color: var(--jonquil);
  color: var(--space-cadet);
}

.accordion-body {
  background-color: var(--mint-green);
  color: var(--space-cadet);
  border-top: 1px solid var(--engineering-orange);
}

/* Survey-specific styles */
.survey-card {
  max-width: 650px;
  width: 90%;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.25);
  border: none;
}

.survey-card-body {
  background-color: var(--space-cadet); 
  color: var(--mint-green);
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  transition: all 0.3s ease;
}

.survey-card .card-header {
  background-color: var(--jonquil);
  color: var(--space-cadet);
}

.survey-progress {
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.2);
}

.survey-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, 
    var(--mint-green) 0%, 
    var(--jonquil) 50%, 
    var(--mint-green) 100%);
  background-size: 200% 100%;
  animation: survey-progress-animation 4s ease infinite;
}

@keyframes survey-progress-animation {
  0% { background-position: 0% 0%; }
  50% { background-position: 100% 0%; }
  100% { background-position: 0% 0%; }
}

.question-text {
  color: var(--jonquil);
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.response-options {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.response-button {
  flex: 1;
  padding: 1rem;
  margin: 0.25rem;
  border: 2px solid var(--tufts-blue);
  background-color: transparent;
  color: var(--mint-green);
  transition: all 0.3s ease;
  font-weight: 500;
  border-radius: 0.5rem;
}

.response-button:hover {
  background-color: var(--tufts-blue-light);
  border-color: var(--tufts-blue-light);
  color: var(--mint-green);
  transform: translateY(-2px);
}

.response-button.active,
.response-button.btn-primary {
  background-color: var(--tufts-blue);
  border-color: var(--tufts-blue);
  color: var(--mint-green);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.response-button.btn-outline-primary {
  background-color: transparent;
  border-color: var(--tufts-blue);
  color: var(--mint-green);
}

.flex-1 {
  flex: 1;
}

.question-container .text-muted {
  color: var(--mint-green) !important;
  opacity: 0.7;
}

/* Navigation buttons */
.nav-button {
  min-width: 100px;
  padding: 0.5rem 1.25rem;
  transition: all 0.3s ease;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.prev-button {
  background-color: rgba(233, 255, 249, 0.1);
  color: var(--mint-green);
  border: 1px solid rgba(233, 255, 249, 0.2);
}

.prev-button:hover {
  background-color: rgba(233, 255, 249, 0.2);
  border-color: var(--mint-green);
}

.next-button {
  background-color: var(--engineering-orange);
  color: var(--mint-green);
  border: 1px solid rgba(233, 255, 249, 0.2);
}

.next-button:hover {
  background-color: var(--engineering-orange);
  border-color: var(--mint-green);
  filter: brightness(1.2);
}

.complete-button {
  background-color: var(--jonquil);
  color: var(--space-cadet);
  border: none;
  padding: 0.5rem 1.5rem;
  font-weight: 600;
}

.complete-button:hover {
  background-color: var(--jonquil);
  filter: brightness(1.1);
  transform: scale(1.05) translateY(-2px);
}

.save-progress-button {
  background-color: rgba(233, 255, 249, 0.1);
  color: var(--jonquil);
  border: 1px solid var(--jonquil);
}

.save-progress-button:hover {
  background-color: rgba(239, 200, 26, 0.2);
  color: var(--jonquil);
  border-color: var(--jonquil);
}

/* Genre selection styles */
#genre-selection .btn-outline-primary {
  color: var(--mint-green);
  border-color: rgba(233, 255, 249, 0.3);
  background-color: rgba(26, 143, 227, 0.1);
  transition: all 0.3s ease;
}

#genre-selection .btn-outline-primary:hover,
#genre-selection .btn-outline-primary:focus {
  background-color: rgba(239, 200, 26, 0.2);
  border-color: var(--jonquil);
  color: var(--jonquil);
  transform: translateY(-2px);
}

#genre-selection .btn-check:checked + .btn-outline-primary {
  background-color: rgba(239, 200, 26, 0.3);
  border-color: var(--jonquil);
  color: var(--jonquil);
  font-weight: 600;
}

/* Responsive styles */
@media (max-height: 800px) {
  .survey-container {
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .survey-container {
    align-items: flex-start;
  }
  
  .card {
    width: 100%;
  }
  
  .response-options {
    flex-direction: column;
  }
  
  .response-button {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

.card-body > p {
  color: var(--space-cadet);
  background-color: var(--mint-green);
  padding: 10px;
  border-radius: 5px;
  border-left: 4px solid var(--tufts-blue);
}

.alert .btn-close {
  color: var(--space-cadet);
  opacity: 0.7;
  text-shadow: none;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232b2d42'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
}

.alert .btn-close:hover,
.alert .btn-close:focus {
  color: var(--space-cadet);
  opacity: 1;
  text-decoration: none;
}

.alert-success .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.alert-danger .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.welcome-modal .modal-body {
  padding: 20px;
  color: var(--jonquil)
}

.welcome-modal .modal-body p,
.welcome-modal .modal-body ul li {
  display: block; 
  margin-bottom: 10px; 
}

.welcome-modal .modal-body ul {
  padding-left: 0; 
  list-style: none;
}

.welcome-modal .modal-body ul li {
  display: flex;
  align-items: flex-start; 
  margin-bottom: 10px; 
}

.welcome-modal .modal-body ul li i {
  margin-right: 10px;
  color: var(--tufts-blue); 
  font-size: 1.5em; 
  line-height: 1; 
}

.welcome-modal .modal-body p,
.welcome-modal .modal-body ul li {
  color: var(--jonquil);
}

.welcome-modal .modal-body ul li span {
  line-height: 1.5;
}

.welcome-modal .modal-content {
  background-color: var(--space-cadet);
  color: var(--mint-green);
  border-radius: 0.5rem;
  border: none;
  overflow: hidden;
}

.welcome-modal .modal-header {
  border-bottom: none;
}

.welcome-modal .modal-title {
  color: var(--jonquil);
  font-weight: bold;
}

.welcome-modal .modal-body {
  padding: 2rem 1.5rem;
}

.welcome-modal .modal-footer {
  border-top: none;
  background-color: rgba(0, 0, 0, 0.1);
}

.welcome-modal .modal-footer .btn-primary {
  background-color: var(--jonquil);
  color: var(--space-cadet);
  border: none;
  font-weight: 600;
  padding: 0.5rem 1.5rem;
}

.welcome-modal .modal-footer .btn-primary:hover {
  filter: brightness(1.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.welcome-modal .modal-content {
  animation: fadeIn 0.5s ease-out;
}

.welcome-modal .modal-body ul li {
  animation: fadeIn 0.5s ease-out;
  animation-fill-mode: both;
}

.welcome-modal .modal-body ul li:nth-child(1) { animation-delay: 0.2s; }
.welcome-modal .modal-body ul li:nth-child(2) { animation-delay: 0.4s; }
.welcome-modal .modal-body ul li:nth-child(3) { animation-delay: 0.6s; }

.show-details {
  cursor: pointer;
}

.footer-link {
  text-decoration: none;
}

.footer-link:hover {
  text-decoration: underline;
}

#watchlistDropdown .dropdown-item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card.bg-secondary {
  transition: background-color 0.3s ease;
}

.card.bg-secondary .card-title,
.card.bg-secondary .card-text {
  transition: color 0.3s ease;
}

.navbar-nav .watchlist-group {
  margin-right: 1rem;
}

.navbar-nav .watchlist-content {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 2px solid var(--jonquil);
  border-radius: 2rem;
  transition: all 0.3s ease;
}

.navbar-nav .watchlist-group:hover .watchlist-content {
  background-color: rgba(239, 200, 26, 0.1);
}

.navbar-nav .watchlist-content .fa-bookmark {
  color: var(--jonquil);
}

.navbar-nav .watchlist-content .font-weight-bold {
  color: var(--mint-green);
  margin-left: 0.5rem;
}

#watchlist-count {
  background-color: var(--engineering-orange);
  color: var(--mint-green);
  border-radius: 1rem;
  padding: 0.25rem 0.5rem;
}

.drag-handle {
  cursor: move;
  user-select: none;
  z-index: 10;
  color: rgba(255, 255, 255, 0.7);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 4px 0;
  transition: all 0.2s ease;
}

.drag-handle:hover {
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(0, 0, 0, 0.7);
}

.watchlist-item:hover .drag-handle {
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(0, 0, 0, 0.7);
}

.blue-background-class {
  background-color: #e0e0e0;
}

.watchlist-container {
  max-height: 800px;
  overflow-y: auto;
  padding: 0.5rem;
}

.container-fluid {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

@media (min-width: 768px) {
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.card-header.unwatched-header,
.card-header.watched-header {
  padding: 0.5rem 0.75rem;
}

.card-header.unwatched-header {
  background-color: var(--jonquil);
  color: var(--space-cadet);
}

.card-header.watched-header {
  background-color: var(--engineering-orange);
  color: var(--mint-green);
}

.card-header .badge {
  font-size: 0.875rem;
  padding: 0.35em 0.65em;
  min-width: 1.5rem;
}

.unwatched-header .badge {
  background-color: var(--engineering-orange) !important;
  color: var(--mint-green) !important;
  border-radius: 1rem !important;
  padding: 0.35em 0.65em !important;
}

.watched-header .badge {
  background-color: var(--mint-green) !important;
  color: var(--space-cadet) !important;
  border-radius: 1rem !important;
  padding: 0.35em 0.65em !important;
  border: none !important;
}

.watchlist-item {
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
}

.watchlist-item:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.watchlist-item .card-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.watchlist-item .card-title {
  font-size: 0.9rem;
  line-height: 1.2;
  color: var(--engineering-orange);
  transition: font-size 0.2s ease;
}

.watchlist-item .card-text-small {
  font-size: 0.75rem;
  line-height: 1.3;
}

.watchlist-item .button-container {
  display: flex;
  gap: 0.25rem;
  align-items: stretch;
}

.watchlist-item .action-buttons {
  display: flex;
  gap: 0.25rem;
  flex: 1;
}

.watchlist-item .btn {
  height: 32px;
  font-size: 0.75rem;
  padding: 0 0.5rem;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.watchlist-item .btn i {
  font-size: 0.875rem;
}

.watchlist-item .mark-watched,
.watchlist-item .mark-unwatched {
  width: 32px;
  padding: 0;
}

.watchlist-item .rate-item {
  flex: 1;
  min-width: 70px;
}

.watchlist-item .square-button {
  width: 32px;
  height: 32px;
  padding: 0;
  flex: 0 0 32px;
}

@media (min-width: 1024px) {
  .watchlist-item .mark-watched,
  .watchlist-item .mark-unwatched {
    width: auto;
    padding: 0 0.5rem;
  }
  
  .watchlist-item .square-button {
    width: auto;
    padding: 0 0.5rem;
    flex: 0 0 auto;
    min-width: 85px;
  }
}

.rating-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
}

.rating-popup-content {
  background-color: var(--space-cadet);
  color: var(--mint-green);
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);
  text-align: center;
  max-width: 90%;
  width: 400px;
}

.rating-popup h3 {
  margin-bottom: 1.5rem;
  color: var(--jonquil);
}

.rating-stars {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.rating-stars .star {
  cursor: pointer;
  color: rgba(255, 255, 255, 0.2);
  transition: color 0.2s ease;
}

.rating-stars .star:hover,
.rating-stars .star.active {
  color: var(--jonquil);
}

.rating-stars:hover .star {
  color: var(--jonquil);
}

.rating-stars .star:hover ~ .star {
  color: rgba(255, 255, 255, 0.2);
}

.rating-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.rating-actions button {
  min-width: 100px;
  padding: 0.5rem 1rem;
}

.card-bg-watchlist-rated {
  background-color: var(--space-cadet);
}

.card-bg-watchlist-watched {
  background-color: #284B63;
}

.card-bg-watchlist-unwatched {
  background-color: var(--tufts-blue);
}

.card-bg-watchlist-rated,
.card-bg-watchlist-watched,
.card-bg-watchlist-unwatched {
  color: var(--mint-green);
}

.card-bg-watchlist-rated .text-muted,
.card-bg-watchlist-watched .text-muted,
.card-bg-watchlist-unwatched .text-muted {
  color: var(--mint-green) !important;
}

.card .fa-bookmark,
.card .fa-eye,
.card .fa-star {
  color: var(--jonquil) !important;
}

.star-score {
  position: relative;
  display: inline-block;
  color: var(--jonquil);
  font-size: 1.25rem; /* Adjust to match other icon sizes */
}

.star-score::before {
  content: '\f005'; /* Star icon */
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.star-score span {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.7rem;
  font-weight: bold;
  color: var(--space-cadet);
}

.card .star-score {
  position: absolute;
  bottom: 0rem;
  right: 0rem;
}

.rate-item {
  width: 100%;
  margin: 0 0 0 0;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.rate-item span {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.rate-item.rated {
  background-color: #198754;
  border-color: #198754;
  color: white;
}

.rate-item.rated .fa-star {
  color: #FFD700;
}

.rating-interface {
  width: 100%;
  padding: 1rem 0;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 0.25rem;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.star {
  cursor: pointer;
  color: #6c757d;
  transition: color 0.2s;
}

.star:hover, .star.active {
  color: #FFD700;
}

.star:hover ~ .star {
  color: #6c757d;
}

.rating-stars:hover .star {
  color: #FFD700;
}

.rating-stars .star:hover ~ .star {
  color: #6c757d;
}

.rating-actions {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.rating-actions button {
  min-width: 80px;
}

.watchlist-item .card-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.watchlist-item .card-buttons .btn {
  flex: 1 1 auto;
  min-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0.375rem 0.75rem;
  height: 31px;
}

@media (max-width: 768px) {
  .watchlist-item .card-buttons {
    flex-direction: column;
  }
  
  .watchlist-item .card-buttons .btn {
    width: 100%;
    min-width: 0;
  }
}

@media (min-width: 1024px) {
  .watchlist-item .card-title {
    font-size: 1.1rem;
    line-height: 1.3;
  }
}

.btn-group-responsive {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (min-width: 400px) {
  .btn-group-responsive {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .btn-group-responsive .btn-group {
    display: flex;
    gap: 0.5rem;
  }
}

.static-card {
  transition: none !important;
}

.static-card:hover {
  transform: none !important;
  box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important;
}

.progress {
  height: 4px;
}

@media (max-width: 768px) {
  .response-options {
    flex-direction: column;
  }
  
  .response-button {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

#complete-survey {
  background-color: var(--golden-glare);
  border-color: var(--engineering-orange);
  color: var(--space-cadet);
  font-weight: bold;
  padding: 0.5rem 1.5rem;
  font-size: 1.1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border-width: 2px;
}

#complete-survey:hover {
  background-color: var(--engineering-orange);
  border-color: var(--golden-glare);
  color: var(--mint-green);
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.25);
}

.bg-space-cadet-light {
  background-color: rgba(43, 45, 66, 0.3);
}

.text-golden-glare {
  color: var(--golden-glare) !important;
}

.btn-golden-glare {
  background-color: var(--golden-glare);
  border-color: var(--golden-glare);
  color: var(--space-cadet);
}

.btn-golden-glare:hover {
  background-color: #ffdf4d;
  border-color: #ffdf4d;
  color: var(--space-cadet);
}

/* Save progress modal styling */
.save-progress-modal .modal-dialog {
  max-width: 420px;
}

.save-progress-modal .modal-content {
  background-color: var(--space-cadet);
  color: var(--mint-green);
  border-radius: 0.5rem;
  border: none;
  overflow: hidden;
}

.save-progress-modal .modal-header {
  background-color: var(--engineering-orange);
  color: var(--space-cadet);
  border-bottom: none;
  padding: 0.5rem 1rem;
}

.save-progress-modal .modal-body {
  padding: 2rem 1.5rem;
}

.save-progress-modal .btn-close {
  color: var(--space-cadet);
  opacity: 0.8;
}

.save-progress-modal .btn-close:hover {
  opacity: 1;
}

/* Responsive styling for save progress modal buttons */
@media (max-width: 576px) {
  .save-progress-modal .d-flex {
    flex-direction: column;
    align-items: center;
  }
  
  .save-progress-modal .d-flex a.btn {
    width: 100% !important;
    margin-bottom: 0.5rem;
  }
  
  .save-progress-modal .modal-body {
    padding: 1.5rem 1rem;
  }
  
  .save-progress-modal .btn {
    font-size: 0.9rem;
    padding: 0.4rem 0.6rem;
  }
}

/* Add smooth transitions for question container */
.survey-container {
  transition: all 0.3s ease;
}

.survey-container {
  transition: all 0.3s ease;
}
