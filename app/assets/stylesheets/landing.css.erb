:root {
  --ffsd: 0px;
  --1vw: calc((100vw - var(--sbw, 0px)) / 100);
  --1vh: var(--inner1Vh, 1vh);
}

@media (prefers-reduced-motion: reduce) {
  .animated {
    animation: none !important;
  }
}

html {
  zoom: var(--rzf, 1);
  font-size: max(calc(min(var(--1vw, 1vw), 13.66px) * var(--rfso, 1)), var(--minfs, 0px));
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}

body {
  font-size: calc(1rem * var(--bfso, 1));
  font-family: var(--bs-font-sans-serif);
  margin: 0;
  padding: 0;
  font-synthesis: none;
  font-kerning: none;
  font-variant-ligatures: none;
  font-feature-settings: "kern"0, "calt"0, "liga"0, "clig"0, "dlig"0, "hlig"0;
  -webkit-font-smoothing: subpixel-antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: geometricprecision;
  white-space: normal;
}

.btn-rounded {
    border-radius: 50px;
    padding: 0.375rem 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
  }

  .btn-outline-mint {
    color: #e9fff9;
    border-color: #e9fff9;
  }

  .btn-outline-mint:hover {
    color: #2b2d42;
    background-color: #efc81a;
    border-color: #efc81a;
  }

  .btn-jonquil {
    color: #2b2d42;
    background-color: #efc81a;
    border-color: #efc81a;
  }

  .btn-jonquil:hover {
    color: #2b2d42;
    background-color: #f0d04b;
    border-color: #f0d04b;
  }

a {
  text-decoration: none;
  color: inherit;
}

img {
  -webkit-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  -webkit-touch-callout: none;
}

.hidden-anchor {
  visibility: hidden;
}

.hero-section {
  position: relative;
  overflow: hidden;
  display: grid;
  align-items: center;
  grid-template-columns: 1fr;
  z-index: 0;
}

.background-image {
  grid-area: 1 / 1 / 2 / 2;
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  z-index: 0;
}

.hero-background {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  animation: fadeIn 1s ease-out;
}

.hero-content {
  grid-area: 1 / 1 / 2 / 2;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 60%;
  padding: 10% 5%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.hero-title {
  color: #efc81a;
  font-family: var(--bs-font-sans-serif);
  font-size: 4.5rem;
  line-height: 1.2;
  letter-spacing: -0.03em;
  text-shadow: 0 0.0375em 0.28125em rgba(0,0,0,0.6);
  margin: 0 0 1.5rem 0;
  font-weight: 700;
}

.hero-subtitle {
  color: #e9fff9;
  font-family: var(--bs-font-sans-serif);
  font-size: 2.5rem;
  line-height: 1.3;
  letter-spacing: -0.03em;
  text-shadow: 0 0.0375em 0.159375em rgba(0,0,0,0.325);
  margin: 0 0 3rem 0;
}

.cta-button-container {
  width: auto;
}

.hero-section .cta-button {
  display: inline-block;
  padding: 1rem 3rem;
  font-size: 1.5rem;
  font-weight: 700;
  text-decoration: none;
  text-transform: uppercase;
  background-color: #efc81a;
  border-radius: 50px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.cta-button:hover {
  background-color: #f7d541;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.features-section {
  position: relative;
  overflow: hidden;
  display: grid;
  justify-items: center;
  align-items: center;
  grid-template-columns: auto 100rem auto;
  z-index: 0;
  background-color: #2b2d42;
}

.features-section .background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(<%= asset_path("landing/6ca309dffd8311f9efcf03dedf9e1623.png") %>);
  background-size: cover;
  background-position: center;
  opacity: 0.5;
  z-index: 0;
}

.features-section .content-wrapper {
  grid-area: 1 / 2 / 2 / 3;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 5%;
  padding-bottom: 5%;
}

.features-section .logo-container {
  width: 30%;
  padding-top: 13.38155515%;
  position: relative;
  margin-bottom: 3rem;
}

.logo {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.feature-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding-bottom: 2rem;
}

.feature {
  text-align: center;
}

.feature-title {
  color: #efc81a;
  font-family: var(--bs-font-sans-serif);
  font-weight: 700;
  font-size: 2.75rem;
  line-height: 1.28570969em;
  margin-bottom: 1rem;
}

.feature-description {
  color: #e9fff9;
  font-family: var(--bs-font-sans-serif);
  font-size: 2rem;
  line-height: 1.26560918em;
  max-width: 80%;
  margin: 0 auto;
}

.hero-content.animate-on-scroll,
.feature.animate-on-scroll {
  opacity: 0;
  transform: translateX(-20px);
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translate(0, 0) scale(1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (min-width: 1025px) {
  .hero-section {
    grid-template-columns: auto 100rem auto;
  }

  .background-image {
    grid-area: 1 / 1 / 2 / 4;
  }

  .hero-content {
    grid-area: 1 / 2 / 2 / 3;
    padding: 15% 40% 5% 5%;
    max-width: 100%;
  }

  .hero-title {
    font-size: 4.5rem;
  }

  .hero-subtitle {
    font-size: 3rem;
  }

  .entertainment-section .content-wrapper {
    max-width: 1200px;
    max-height: 600px;
  }
}

@media (max-width: 1024px) {
  .hero-section {
    grid-template-columns: 1fr;
    text-align: left;
  }

  .hero-content {
    padding: 10% 5% 10% 5%;
    max-width: 55%;
    justify-self: start;
    align-items: flex-start;
  }

  .hero-title {
    font-size: 4rem;
    text-align: left;
  }

  .hero-subtitle {
    font-size: 2.5rem;
    text-align: left;
  }

  .cta-button-container {
    width: 100%;
    display: flex;
    justify-content: flex-start; /* Align the button to the start */
  }

  .hero-section .cta-button {
    font-size: 2rem;
  }

  .features-section .logo-container {
    width: 40%;
  }
}

@media (max-width: 768px) {
  .hero-section {
    grid-template-columns: 1fr;
  }

  .hero-content {
    padding: 10% 5% 5% 5%;
    align-items: flex-start;
    max-width: 60%;
  }

  .hero-title {
    font-size: 4.5rem;
  }

  .hero-subtitle {
    font-size: 3rem;
  }

  .hero-section .cta-button {
    font-size: 2.5rem;
  }

  .cta-button-container {
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }
}

.entertainment-section {
  position: relative;
  aspect-ratio: 16 / 8;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 5%;
}

.entertainment-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom left, #efc81a, #efc81a 20%, #ba2d0b 40%, #ba2d0b);
  z-index: -1;
}

.content-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 1200px;
}

.heading {
  position: absolute;
  left: 15%;
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  line-height: 1.2;
  color: #e9fff9;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.laptop-image {
  position: absolute;
  bottom: 5%;
  left: 5%;
  width: 47%;
}

.iphone-image {
  position: absolute;
  top: 5%;
  right: 20%;
  max-height: 65%;
  max-width: 250px;
}

.description {
  position: absolute;
  bottom: 0%;
  left: 60%;
  font-size: 2rem;
  line-height: 1.5;
  color: #e9fff9;
  text-align: left;
  max-width: 45%;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

@media (max-width: 1024px) {
  .entertainment-section {
    aspect-ratio: auto;
    height: auto;
    min-height: fit-content;
    padding: 5% 0;
  }

  .entertainment-section .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: auto;
    padding: 0 5%;
  }
  
  .entertainment-section .heading,
  .entertainment-section .laptop-image,
  .entertainment-section .iphone-image,
  .entertainment-section .description {
    position: static;
    margin: 2rem 0;
    text-align: center;
    width: 100%;
    max-width: 100%;
  }
  
  .entertainment-section .heading {
    font-size: 4rem;
    padding-bottom: 1rem;
  }
  
  .entertainment-section .laptop-image,
  .entertainment-section .iphone-image {
    max-width: 200px;
    height: auto;
    object-fit: contain;
  }
  
  .entertainment-section .description {
    font-size: 2rem;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 768px) {
  .entertainment-section .description {
    font-size: 2rem;
  }
  
  .cta-section {
    padding: 3rem;
  }
  
  .cta-button {
    font-size: 3rem;
    padding: 1rem 3rem;
  }
  
  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }  
  .footer-logo {
    width: 150px;
  }
  
  .questions {
    font-size: 3.5rem;
  }
  
  .email {
    font-size: 2.5rem;
  }
}

.testimonials-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #2b2d42;
  padding: 5% 5%;
}

.testimonials-section .background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(<%= asset_path("landing/6ca309dffd8311f9efcf03dedf9e1623.png") %>);
  background-size: cover;
  background-position: center;
  opacity: 0.5;
  z-index: 0;
}

.testimonials-section .content-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  color: #e9fff9;
}

.section-title {
  font-size: 3.25rem;
  color: #efc81a;
  text-align: center;
  margin-bottom: 3rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.testimonials-container {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  flex-wrap: wrap;
}

.testimonial {
  flex: 1;
  min-width: 250px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.image-container {
  width: 200px;
  height: 200px;
  margin-bottom: 2rem;
  border-radius: 10px;
  overflow: hidden;
}

.user-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.michael-image {
  object-position: center 30%;
}

.emily-image {
  object-position: 95% center;
}

.user-name {
  font-size: 2.5rem;
  color: #efc81a;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.user-quote {
  font-size: 1.75rem;
  line-height: 1.4;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
  max-width: 90%;
  margin: 0 auto;
}

@media (max-width: 1024px) {
  .testimonials-section {
    padding: 5%;
    height: auto;
    min-height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .testimonials-section .content-wrapper {
    padding: 0;
  }

  .testimonials-section .section-title {
    font-size: 4rem;
    margin-block-start: -2%;
    margin-block-end: 6%;
  }

  .section-title {
    font-size: 3.25rem;
    margin-bottom: 2rem;
  }

  .testimonials-container {
    flex-direction: column;
    align-items: center;
  }

  .testimonial {
    max-width: 100%;
  }

  .image-container {
    width: 150px;
    height: 150px;
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .testimonials-section {
    padding: 5%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .testimonials-section .section-title {
    font-size: 4rem;
    margin-block-start: -2%;
    margin-block-end: 6%;
  }

  .section-title {
    font-size: 4rem;
    margin-bottom: 2rem;
  }

   .image-container {
    width: 120px;
    height: 120px;
    margin-bottom: 2rem;
  }

  .user-name {
    font-size: 3rem;
  }

  .user-quote {
    font-size: 2rem;
  }
}

.video-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom right, #ba2d0b, #1a8fe3);
  padding: 5%;
  overflow: hidden;
}

.content-wrapper {
  width: 100%;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  font-size: 4rem;
  color: #efc81a;
  text-align: center;
  margin-bottom: 3rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.video-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-button {
  position: absolute;
  background-color: rgba(239, 200, 26, 0.8);
  border: 3px solid #e9fff9;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-button i {
  color: #2b2d42;
  font-size: 40px;
}

.play-button:hover {
  background-color: rgba(186, 45, 11, 0.8);
  transform: scale(1.1);
}

.play-button:hover i {
  color: #e9fff9;
}

@media (max-width: 1024px) {
  .section-title {
    font-size: 4rem;
  }

  .play-button {
    width: 60px;
    height: 60px;
  }

  .play-button i {
    font-size: 30px;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 4rem;
  }

  .play-button {
    width: 50px;
    height: 50px;
  }

  .play-button i {
    font-size: 25px;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 4rem;
  }

  .play-button {
    width: 40px;
    height: 40px;
  }

  .play-button i {
    font-size: 20px;
  }
}

.cta-section {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #2b2d42;
  padding: 5%;
}

.cta-section .cta-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(<%= asset_path("landing/4a2553e5c1a52b8ec93fd54140badd4e.png") %>);
  background-size: cover;
  background-position: center;
  opacity: 0.5;
  z-index: 0;
}

.cta-section .content-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  height: 100%;
  color: #e9fff9;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

.cta-button {
  display: inline-block;
  padding: 1rem 3rem;
  background-color: #ba2d0b;
  text-decoration: none;
  font-weight: bold;
  border-radius: 5px;
  margin-top: 2rem;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background-color: #8a210a;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 3rem;
}

.logo-copyright {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer-logo {
  height: 50px;
}

.contact-info {
  text-align: right;
}

.questions {
  margin: 0;
  font-weight: bold;
}

.email {
  color: #efc81a;
  text-decoration: none;
}

/* Animation styles */
.animate-on-scroll {
  opacity: 0;
  transition: opacity 0.7s ease-out, transform 0.7s ease-out; /* Faster animation */
}

.animate-on-scroll[data-animation="baseline"] {
  transform: translateY(20px);
}

.animate-on-scroll[data-animation="pop"] {
  transform: scale(0.9);
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translate(0, 0) scale(1);
}
  
.cta-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(<%= asset_path("landing/4a2553e5c1a52b8ec93fd54140badd4e.png") %>);
  background-size: cover;
  background-position: center;
  opacity: 0.5;
}
  
.content-wrapper {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1200px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
  
.cta-button {
  background-color: #efc81a; /* jonquil */
  color: #ba2d0b; /* engineering orange */
  font-size: 2rem;
  font-weight: bold;
  text-decoration: none;
  padding: 1rem 3rem;
  border-radius: 50px;
  margin-top: 2rem;
  transition: background-color 0.3s ease, transform 0.3s ease;
  shadow: 2px 2px 4px rgba(0,0,0,0.5);
}
  
.cta-button:hover {
  background-color: #f0d04b;
  transform: scale(1.1);
}
  
.footer-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}
  
.footer-logo {
  width: 250px;
}
  
.footer-link, .copyright {
  color: #e9fff9;
  text-decoration: none;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}
  
.footer-link:hover {
  color: #ba2d0b;
}
  
/* Animation styles */
.animate-on-scroll {
  opacity: 0;
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}
  
.animate-on-scroll[data-animation="baseline"] {
  transform: translateY(20px);
}
  
.animate-on-scroll[data-animation="pop"] {
  transform: scale(0.95);
}
  
.animate-on-scroll[data-animation="fade-in"] {
  opacity: 0;
  transition: opacity 0.7s ease-out;
}

.animate-on-scroll[data-animation="fade-in"].animated {
  opacity: 1;
}
  
@media (max-width: 768px) {
  .cta-button {
    font-size: 2.5rem;
  }
  
  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .logo-copyright, .contact-info {
    align-items: center;
    margin-top: 3rem;
  }
  
  .footer-logo {
    width: 150px;
  }
}

@media (max-width: 480px) {
  .hero-section .cta-button {
    font-size: 3rem;
    padding: 1.5rem 4.5rem;
    margin-left: 0;
  }
  
  .feature-title {
    font-size: 4rem;
  }

  .feature-description {
    font-size: 2.75rem;
  }

  .entertainment-section .heading {
    font-size: 4rem;
    margin-block-start: -6%;
    margin-block-end: 6%;
  }

  .entertainment-section .description {
    font-size: 2.75rem;
  }

  .testimonials-section .section-title {
    font-size: 4rem;
    margin-block-start: -4%;
    margin-block-end: 10%;
  }

  .testimonials-section .image-container {
    width: 100px;
    height: 100px;
    margin-bottom: 3rem;
  }

  .testimonials-section .user-name {
    font-size: 3.25rem;
  }

  .testimonials-section .user-quote {
    font-size: 2.75rem;
  }

  .video-section {
    padding-bottom: 8%;
      }

  .cta-section .section-title {
    font-size: 4rem;
  }
        
  .cta-section .cta-button {
    font-size: 3rem;
    padding: 1.5rem 4.5rem;
    margin-top: 3rem;
  }

  .footer-content {
    padding-bottom: 10%;
  }

  .footer-link, .copyright {
    font-size: 2rem;
  }

  .questions {
    font-size: 4rem;
  }

  .email {
    font-size: 3.25rem;
  }
    }

.navbar .nav-link {
    font-size: 1.5rem;
  }

.btn {
    --bs-btn-font-size: 1.25rem;
  }

.btn-rounded {
    padding: 0.375rem 1.25rem;
  }

@media (max-width: 1023px) {
  .navbar {
    --bs-navbar-toggler-font-size: 2.5rem;
    padding: 10px 20px;
  }

  .navbar .nav-link {
    font-size: 2.25rem;
  }

  .btn {
    --bs-btn-font-size: 2.25rem;
  }

  .btn-rounded {
    padding: 0.375rem 2.25rem;
  }
}

@media (max-width: 768px) {
  .navbar {
    --bs-navbar-toggler-font-size: 2.75rem;
  }

  .navbar .nav-link {
    font-size: 2.5rem;
  }

  .btn {
    --bs-btn-font-size: 2.5rem;
  }
 
 .btn-rounded {
    padding: 0.425rem 2.5rem;
  }
}

@media (max-width: 480px) {
  .navbar {
    --bs-navbar-toggler-font-size: 5rem;
  }

  .navbar .nav-link {
    font-size: 4rem;
  }

  .btn {
    --bs-btn-font-size: 4rem;
  }

  .btn-rounded {
    padding: 0.5rem 4rem;
  }
}
